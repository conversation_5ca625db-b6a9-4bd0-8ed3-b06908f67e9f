import React, { useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';

const RefundPolicyPage = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Set page title and meta description
  useEffect(() => {
    document.title = 'Refund Policy | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Refund policy for VerifiedOnward.com - All sales are final due to the instant digital nature of our flight reservations.';

    // Add structured data for FAQ schema
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Why are refunds not possible?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Because each document is generated on-demand with real-time flight data and personal travel information, we are unable to offer refunds under any circumstances. Our system immediately processes your request and delivers your reservation document within minutes."
          }
        },
        {
          "@type": "Question",
          "name": "What if I entered incorrect details?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We encourage users to carefully review all input details before completing their purchase. Please double-check flight details, passenger information, and travel dates as documents cannot be modified once created."
          }
        },
        {
          "@type": "Question",
          "name": "How can I ensure my ticket meets embassy standards?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Every reservation is formatted to meet embassy standards and ensure smooth visa processing. Our documents are professionally formatted with authentic airline data and verifiable booking references, maintaining a 99.7% acceptance rate."
          }
        }
      ]
    };

    // Add structured data to head
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]');
    if (!structuredDataScript) {
      structuredDataScript = document.createElement('script');
      structuredDataScript.type = 'application/ld+json';
      document.head.appendChild(structuredDataScript);
    }
    structuredDataScript.textContent = JSON.stringify(structuredData);

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservations';
      // Remove structured data on cleanup
      const script = document.querySelector('script[type="application/ld+json"]');
      if (script) {
        script.remove();
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              REFUND POLICY
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Refund
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Policy</span>
            </h1>
          </motion.div>
        </div>
      </section>

      {/* Premium Content Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="premium-card max-w-4xl mx-auto p-12"
          >
            <div className="space-y-12">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-brand-800 mb-4">Policy Overview</h2>
                <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
              </div>

              <div className="space-y-10">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 p-6 rounded-r-xl shadow-soft hover:shadow-aviation transition-all duration-300"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <span className="text-2xl mr-3">🚫</span>
                    Final Sale Policy
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    <strong>All sales are final and non-refundable.</strong> Due to the nature and affordability of our service, VerifiedOnward.com provides a low-cost, digital product delivered instantly upon payment.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 p-6 rounded-r-xl shadow-soft hover:shadow-aviation transition-all duration-300"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <span className="text-2xl mr-3">ℹ️</span>
                    Why No Refunds?
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    Because each document is generated on-demand with real-time flight data and personal travel information, we are unable to offer refunds under any circumstances. Our system immediately processes your request and delivers your reservation document within minutes.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500 p-6 rounded-r-xl shadow-soft hover:shadow-aviation transition-all duration-300"
                >
                  <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                    <span className="text-2xl mr-3">✅</span>
                    Before You Purchase
                  </h3>
                  <p className="text-neutral-700 text-lg leading-relaxed">
                    We encourage users to carefully review all input details before completing their purchase. Please double-check flight details, passenger information, and travel dates as documents cannot be modified once created.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-8 rounded-r-xl shadow-soft hover:shadow-aviation transition-all duration-300 border border-brand-200"
                >
                  <div className="bg-gradient-to-br from-white/50 to-brand-50/50 p-6 rounded-xl">
                    <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                      <svg className="w-6 h-6 mr-3 text-brand-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                      Need Support?
                    </h3>
                    <p className="text-neutral-700 text-lg leading-relaxed">
                      For support or questions, please contact us at{' '}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-brand-600 hover:text-brand-800 font-semibold underline decoration-2 underline-offset-2 hover:decoration-brand-800 transition-all duration-200"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gradient-to-br from-neutral-50 to-brand-50/20">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-4">
                Frequently Asked Questions
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
            </div>

            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="premium-card p-8"
              >
                <h3 className="text-xl font-bold text-neutral-800 mb-4">
                  Why are refunds not possible?
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  Because each document is generated on-demand with real-time flight data and personal travel information, we are unable to offer refunds under any circumstances. Our system immediately processes your request and delivers your reservation document within minutes.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="premium-card p-8"
              >
                <h3 className="text-xl font-bold text-neutral-800 mb-4">
                  What if I entered incorrect details?
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  We encourage users to carefully review all input details before completing their purchase. Please double-check flight details, passenger information, and travel dates as documents cannot be modified once created.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="premium-card p-8"
              >
                <h3 className="text-xl font-bold text-neutral-800 mb-4">
                  How can I ensure my ticket meets embassy standards?
                </h3>
                <p className="text-neutral-700 text-lg leading-relaxed">
                  Every reservation is formatted to meet embassy standards and ensure smooth visa processing. Our documents are professionally formatted with authentic airline data and verifiable booking references, maintaining a 99.7% acceptance rate.
                </p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Trust Badges Section */}
      <section className="py-12 bg-white">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex flex-wrap justify-center items-center gap-8 mb-8">
              <div className="flex items-center bg-green-50 text-green-800 px-6 py-3 rounded-full font-semibold">
                <span className="text-2xl mr-3">✅</span>
                99.7% Acceptance Rate
              </div>
              <div className="flex items-center bg-blue-50 text-blue-800 px-6 py-3 rounded-full font-semibold">
                <span className="text-2xl mr-3">👥</span>
                75,000+ Travelers Trust Us
              </div>
              <div className="flex items-center bg-purple-50 text-purple-800 px-6 py-3 rounded-full font-semibold">
                <span className="text-2xl mr-3">🔒</span>
                Secure Purchase
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl max-w-3xl mx-auto">
              <p className="text-lg text-neutral-700 font-medium">
                While refunds are not possible, every reservation is formatted to meet embassy standards and ensure smooth visa processing.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium CTA Section */}
      <section className="py-16 bg-gradient-to-br from-brand-50/30 to-accent-50/20">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="premium-card p-12 text-center bg-gradient-to-br from-white via-white to-brand-50/30">
              {/* Premium background effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5 rounded-2xl"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-brand-400/10 to-accent-400/10 rounded-full blur-3xl"></div>

              <div className="relative z-10">
                <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-6">
                  ✅ Need an embassy-approved flight reservation?
                </h2>

                <p className="text-xl text-brand-700 mb-8 leading-relaxed max-w-2xl mx-auto">
                  Get one securely in just 60 seconds. Professional format, real flight data, instant download.
                </p>

                {/* Trust badges */}
                <div className="flex flex-wrap justify-center gap-4 mb-10">
                  <span className="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm text-brand-800 text-sm font-semibold rounded-full shadow-soft">
                    ✅ Instant Download
                  </span>
                  <span className="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm text-green-800 text-sm font-semibold rounded-full shadow-soft">
                    ✅ Embassy Approved
                  </span>
                  <span className="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm text-blue-800 text-sm font-semibold rounded-full shadow-soft">
                    ✅ Real Flight Data
                  </span>
                </div>

                {/* CTA Button */}
                <Link
                  className="inline-flex items-center px-10 py-5 bg-gradient-to-r from-brand-600 to-accent-600 hover:from-brand-700 hover:to-accent-700 text-white text-xl font-bold rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-aviation hover:shadow-aviation-hover group"
                  {...getCTANavigationProps(navigate, location.pathname)}
                >
                  Get Your Reservation Now
                  <svg className="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>

                {/* Trust Footer */}
                <div className="mt-8 text-brand-600 font-medium">
                  <p>✓ Secure payment • ✓ 60-second delivery • ✓ Email support</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default RefundPolicyPage;
