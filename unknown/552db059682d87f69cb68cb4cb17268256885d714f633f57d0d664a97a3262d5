import React from 'react';

const BlogThumbnail = ({ thumbnail, title }) => {
  if (!thumbnail) {
    // Default thumbnail if none provided
    return (
      <div className="w-full h-full bg-gradient-to-br from-brand-500 to-brand-600 rounded-xl flex items-center justify-center">
        <div className="text-white text-2xl sm:text-3xl md:text-4xl">📄</div>
      </div>
    );
  }

  const getPatternBackground = (pattern) => {
    switch (pattern) {
      case 'europe':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-8 h-8 border-2 border-white rounded-full"></div>
            <div className="absolute top-8 right-6 w-6 h-6 border-2 border-white rounded-full"></div>
            <div className="absolute bottom-6 left-8 w-4 h-4 border-2 border-white rounded-full"></div>
            <div className="absolute bottom-4 right-4 w-5 h-5 border-2 border-white rounded-full"></div>
          </div>
        );
      case 'tickets':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-6 left-6 w-16 h-10 border-2 border-white rounded transform rotate-12"></div>
            <div className="absolute bottom-8 right-8 w-14 h-8 border-2 border-white rounded transform -rotate-12"></div>
          </div>
        );
      case 'warning':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 right-4 w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-white"></div>
            <div className="absolute bottom-6 left-6 w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-white"></div>
          </div>
        );
      case 'flight':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-1/2 left-1/4 w-12 h-1 bg-white transform -translate-y-1/2"></div>
            <div className="absolute top-1/2 right-1/4 w-8 h-1 bg-white transform -translate-y-1/2"></div>
            <div className="absolute top-1/3 left-1/2 w-1 h-8 bg-white transform -translate-x-1/2"></div>
          </div>
        );
      case 'legal':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-6 left-1/2 w-8 h-8 border-2 border-white rounded-full transform -translate-x-1/2"></div>
            <div className="absolute bottom-6 left-1/2 w-12 h-2 bg-white rounded transform -translate-x-1/2"></div>
          </div>
        );
      case 'embassy':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-6 h-8 border-2 border-white"></div>
            <div className="absolute top-4 right-4 w-6 h-8 border-2 border-white"></div>
            <div className="absolute bottom-4 left-1/2 w-8 h-2 bg-white transform -translate-x-1/2"></div>
          </div>
        );
      case 'return':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-1/2 left-4 w-8 h-1 bg-white transform -translate-y-1/2"></div>
            <div className="absolute top-1/2 right-4 w-8 h-1 bg-white transform -translate-y-1/2"></div>
            <div className="absolute top-1/2 left-1/2 w-1 h-8 bg-white transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>
        );
      case 'safety':
      case 'security':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-1/2 w-8 h-8 border-2 border-white rounded-full transform -translate-x-1/2"></div>
            <div className="absolute bottom-4 left-1/2 w-6 h-6 border-2 border-white transform -translate-x-1/2"></div>
          </div>
        );
      case 'savings':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 right-4 w-6 h-6 border-2 border-white rounded-full"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 border-2 border-white rounded-full"></div>
            <div className="absolute top-1/2 left-1/2 w-4 h-4 bg-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>
        );
      case 'mistakes':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-4 h-4 bg-white transform rotate-45"></div>
            <div className="absolute top-4 right-4 w-4 h-4 bg-white transform rotate-45"></div>
            <div className="absolute bottom-4 left-1/2 w-4 h-4 bg-white transform rotate-45 -translate-x-1/2"></div>
          </div>
        );
      case 'timing':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-1/2 left-1/2 w-12 h-12 border-2 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute top-1/2 left-1/2 w-1 h-4 bg-white transform -translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute top-1/2 left-1/2 w-3 h-1 bg-white transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>
        );
      case 'requirements':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-8 h-1 bg-white"></div>
            <div className="absolute top-8 left-4 w-6 h-1 bg-white"></div>
            <div className="absolute top-12 left-4 w-10 h-1 bg-white"></div>
            <div className="absolute bottom-4 right-4 w-4 h-4 border-2 border-white"></div>
          </div>
        );
      case 'tourism':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-1/2 w-8 h-4 bg-white rounded-full transform -translate-x-1/2"></div>
            <div className="absolute bottom-4 left-4 w-4 h-4 bg-white rounded-full"></div>
            <div className="absolute bottom-4 right-4 w-4 h-4 bg-white rounded-full"></div>
          </div>
        );
      case 'business':
        return (
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-6 h-8 border-2 border-white"></div>
            <div className="absolute top-4 right-4 w-6 h-8 border-2 border-white"></div>
            <div className="absolute bottom-4 left-1/2 w-8 h-4 border-2 border-white transform -translate-x-1/2"></div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`w-full h-full bg-gradient-to-br ${thumbnail.colors.join(' ')} rounded-xl relative overflow-hidden flex items-center justify-center group-hover:scale-105 transition-transform duration-300`}>
      {/* Pattern Background */}
      {getPatternBackground(thumbnail.pattern)}

      {/* Main Icon */}
      <div className="text-white text-3xl sm:text-4xl md:text-5xl z-10 drop-shadow-lg">
        {thumbnail.icon}
      </div>

      {/* Subtle overlay for better text contrast */}
      <div className="absolute inset-0 bg-black/10"></div>

      {/* Hover effect */}
      <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 transition-colors duration-300"></div>
    </div>
  );
};

export default BlogThumbnail;
