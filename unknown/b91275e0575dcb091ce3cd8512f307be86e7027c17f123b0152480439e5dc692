import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

const TermsOfServicePage = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  // Set page title and enhanced meta tags for SEO
  useEffect(() => {
    document.title = 'Terms of Service | VerifiedOnward - Legal Terms & Conditions';

    // Update meta description for better SEO
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Complete Terms of Service for VerifiedOnward flight reservation service. Legal terms, refund policy, user responsibilities, and embassy-approved document policies for visa applications.';

    // Add meta keywords for SEO
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta');
      metaKeywords.name = 'keywords';
      document.head.appendChild(metaKeywords);
    }
    metaKeywords.content = 'terms of service, flight reservation legal terms, visa document policy, embassy approved reservations, refund policy, user agreement';

    // Add structured data for legal service and FAQ
    const structuredData = [
      {
        "@context": "https://schema.org",
        "@type": "LegalService",
        "name": "VerifiedOnward Terms of Service",
        "description": "Legal terms and conditions for VerifiedOnward embassy-approved flight reservation service",
        "provider": {
          "@type": "Organization",
          "name": "VerifiedOnward",
          "url": "https://verifiedonward.com",
          "logo": "https://verifiedonward.com/logo.png"
        },
        "serviceType": "Flight Reservation Service",
        "areaServed": "Worldwide",
        "url": "https://verifiedonward.com/terms-of-service",
        "inLanguage": "en-US",
        "dateModified": new Date().toISOString().split('T')[0]
      },
      {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": termsPageFAQs.map(faq => ({
          "@type": "Question",
          "name": faq.question,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": faq.answer
          }
        }))
      }
    ];

    // Create and add the JSON-LD script tags
    // Remove existing schema scripts
    document.querySelectorAll('script[type="application/ld+json"][data-schema="terms"]').forEach(script => script.remove());

    // Add each structured data object as a separate script
    structuredData.forEach((schema, index) => {
      const schemaScript = document.createElement('script');
      schemaScript.type = 'application/ld+json';
      schemaScript.setAttribute('data-schema', 'terms');
      schemaScript.setAttribute('data-index', index.toString());
      schemaScript.textContent = JSON.stringify(schema);
      document.head.appendChild(schemaScript);
    });

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
      // Remove structured data scripts
      document.querySelectorAll('script[type="application/ld+json"][data-schema="terms"]').forEach(script => script.remove());
    };
  }, []);

  // FAQ data for the Terms page
  const termsPageFAQs = [
    {
      question: "Are VerifiedOnward flight reservations legally valid for visa applications?",
      answer: "Yes, our flight reservations are professionally formatted documents that display authentic airline data and meet embassy requirements for visa applications. They are specifically designed for documentation purposes and are widely accepted by embassies worldwide."
    },
    {
      question: "Can I get a refund if my visa is rejected?",
      answer: "All sales are final and non-refundable due to the instant digital delivery nature of our service. This policy applies regardless of visa outcomes, as embassy decisions are beyond our control and our service has already been delivered upon purchase."
    },
    {
      question: "Do embassies accept these flight reservation documents?",
      answer: "Yes, our documents are formatted to meet standard embassy requirements and display real flight data. However, each embassy has its own policies, and we cannot guarantee acceptance as final decisions rest with visa officers."
    },
    {
      question: "What are my responsibilities when using this service?",
      answer: "You must provide accurate passenger details, use the service only for legitimate visa applications, and understand that these are reservation documents for documentation purposes only - not valid tickets for actual travel."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              LEGAL TERMS & CONDITIONS
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Terms of
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Service</span>
            </h1>


          </motion.div>
        </div>
      </section>

      {/* Premium Content Section */}
      <section className="py-16">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="premium-card max-w-5xl mx-auto p-12"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-brand-800 mb-4">Service Agreement</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
              <p className="text-neutral-600 mt-4 max-w-2xl mx-auto">
                By using VerifiedOnward, you agree to these terms and conditions. Our service provides embassy-approved flight reservations for visa documentation purposes.
              </p>
            </div>

            <div className="space-y-10">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-brand-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">1</span>
                  Purpose of Service
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    VerifiedOnward provides professionally formatted <strong>embassy-approved flight reservations</strong> for visa application purposes only. These documents display authentic airline data and meet standard embassy requirements for travel documentation.
                  </p>
                  <p>
                    <strong>Important:</strong> These are reservation documents for documentation purposes only and are <em>not valid tickets for actual travel, check-in, or boarding</em>. They cannot be used to board flights or claim airline services.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">2</span>
                  Accuracy of Data
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    Our flight data is sourced from <strong>real airline schedules and routing systems</strong>, ensuring authentic flight numbers, times, and routes that match actual airline operations.
                  </p>
                  <p>
                    <strong>Disclaimer:</strong> While we use current airline data, we cannot guarantee real-time seat availability, pricing accuracy, or flight schedule changes. Airlines may modify schedules, and actual flight availability may differ from our documentation.
                  </p>
                  <p>
                    The reservation documents are formatted for visa application purposes and reflect realistic travel itineraries based on standard airline routes and schedules.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">3</span>
                  Pricing & Payment
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    Our transparent pricing structure: <strong>$4.99 USD per one-way reservation</strong> or <strong>$9.98 USD for return trips</strong>. No hidden fees, no subscription charges.
                  </p>
                  <p>
                    <strong>Secure Payment:</strong> All transactions are processed through industry-leading payment providers (Stripe and PayPal) with bank-level encryption and security standards.
                  </p>
                  <p>
                    Payment is required before document generation. Upon successful payment, your reservation document will be available for immediate download and sent to your email address.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">4</span>
                  Sales & Refund Policy
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    <strong>All sales are final and non-refundable.</strong> Due to the instant digital delivery nature of our service and the low pricing model, we cannot offer refunds under any circumstances.
                  </p>
                  <p>
                    This no-refund policy applies to all situations including, but not limited to:
                  </p>
                  <ul className="list-disc list-inside space-y-2 ml-6 text-neutral-600">
                    <li>Incorrect passenger details or travel information provided by the customer</li>
                    <li>Change of mind or travel plan modifications</li>
                    <li>Visa application rejections or embassy decisions</li>
                    <li>Duplicate orders or accidental purchases</li>
                    <li>Technical issues on the customer's end preventing download</li>
                    <li>Dissatisfaction with document format or appearance</li>
                  </ul>
                  <p>
                    <strong>Important:</strong> Please review all details carefully before completing your purchase, as our service is delivered immediately upon payment confirmation.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-l-4 border-yellow-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-yellow-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">5</span>
                  User Responsibilities
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    By using our service, you agree to the following responsibilities:
                  </p>
                  <ul className="list-disc list-inside space-y-2 ml-6 text-neutral-600">
                    <li><strong>Accurate Information:</strong> Provide correct passenger details, travel dates, and destinations</li>
                    <li><strong>Legitimate Use:</strong> Use reservation documents only for legitimate visa applications</li>
                    <li><strong>No Misrepresentation:</strong> Do not present documents as actual flight tickets to airlines or for boarding</li>
                    <li><strong>Compliance:</strong> Ensure your use complies with embassy requirements and local laws</li>
                    <li><strong>Single Use:</strong> Each reservation is for one-time use for the specified visa application</li>
                  </ul>
                  <p>
                    You acknowledge that misuse of our service or providing false information may result in service termination and potential legal consequences.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-gradient-to-r from-purple-50 to-purple-100 border-l-4 border-purple-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">6</span>
                  Liability & No Guarantee
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    <strong>Embassy Decisions:</strong> We are not liable for decisions made by embassies, consulates, or visa officers. Visa approval or rejection is entirely at the discretion of the relevant authorities.
                  </p>
                  <p>
                    <strong>Service Limitations:</strong> We provide supporting travel documentation in a format typically accepted for visa processing. We cannot guarantee embassy acceptance, visa approval, or any specific outcome.
                  </p>
                  <p>
                    <strong>No Warranties:</strong> Our service is provided "as is" without warranties of any kind. We disclaim all liability for indirect, incidental, or consequential damages arising from service use.
                  </p>
                  <p>
                    <strong>Maximum Liability:</strong> Our total liability is limited to the amount paid for the service ($4.99 per reservation).
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="bg-gradient-to-r from-indigo-50 to-indigo-100 border-l-4 border-indigo-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">7</span>
                  Intellectual Property
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    All content, branding, logos, document templates, and proprietary formatting used by VerifiedOnward are protected by intellectual property laws.
                  </p>
                  <p>
                    <strong>Limited License:</strong> You receive a limited, non-transferable license to use the reservation document solely for your visa application purposes.
                  </p>
                  <p>
                    <strong>Prohibited Uses:</strong> You may not reproduce, distribute, modify, or create derivative works from our documents, templates, or branding without explicit written permission.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="bg-gradient-to-r from-gray-50 to-gray-100 border-l-4 border-gray-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-gray-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">8</span>
                  Governing Law
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    These Terms of Service are governed by and construed in accordance with the laws of the United Kingdom, without regard to conflict of law principles.
                  </p>
                  <p>
                    Any disputes arising from these terms or use of our service shall be subject to the exclusive jurisdiction of the courts of England and Wales.
                  </p>
                  <p>
                    <strong>Severability:</strong> If any provision of these terms is found unenforceable, the remaining provisions shall remain in full force and effect.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-gradient-to-r from-brand-50 to-brand-100 border-l-4 border-brand-500 p-8 rounded-r-xl"
              >
                <h3 className="text-2xl font-bold text-neutral-800 mb-4 flex items-center">
                  <span className="bg-brand-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4">9</span>
                  Contact Information
                </h3>
                <div className="text-neutral-700 text-lg leading-relaxed space-y-4">
                  <p>
                    For questions about our Terms of Service, technical support, or general inquiries, please contact us at:{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-brand-600 hover:text-brand-800 font-semibold underline decoration-2 underline-offset-2 hover:decoration-brand-800 transition-all duration-200"
                    >
                      <EMAIL>
                    </a>
                  </p>
                  <p>
                    <strong>Response Time:</strong> We typically respond to all inquiries within 24 hours during business days.
                  </p>
                  <p>
                    <strong>Last Updated:</strong> These Terms of Service were last updated on {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}.
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gradient-to-br from-brand-50/30 to-neutral-50">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-brand-800 mb-4">Frequently Asked Questions</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full mb-4"></div>
              <p className="text-neutral-600 max-w-2xl mx-auto">
                Common questions about our Terms of Service and legal policies.
              </p>
            </div>

            <div className="space-y-4">
              {termsPageFAQs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-5 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-inset"
                    aria-expanded={openFAQ === index}
                    aria-controls={`faq-answer-${index}`}
                  >
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {faq.question}
                    </h3>
                    <svg
                      className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                        openFAQ === index ? 'transform rotate-180' : ''
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <AnimatePresence>
                    {openFAQ === index && (
                      <motion.div
                        id={`faq-answer-${index}`}
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-5 pt-2">
                          <p className="text-gray-700 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Soft Conversion Section */}
      <section className="py-12">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="bg-gradient-to-r from-brand-500/10 via-blue-500/10 to-accent-500/10 rounded-2xl p-8 text-center border border-brand-200/50">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-500 rounded-full mb-6">
                <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-brand-800 mb-4">Ready to Apply?</h3>
              <p className="text-lg text-neutral-700 mb-6 max-w-2xl mx-auto">
                Get your embassy-approved flight reservation instantly. Trusted by 75,000+ travelers worldwide with transparent terms and secure service.
              </p>
              <Link
                to="/"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-brand-500 to-brand-600 text-white text-lg font-semibold rounded-full hover:from-brand-600 hover:to-brand-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Generate Your Reservation Securely →
              </Link>
              <div className="mt-4 text-sm text-neutral-600">
                ✓ 99.8% Embassy Acceptance • ✓ Instant Download • ✓ Secure Payment
              </div>
            </div>
          </motion.div>
        </div>
      </section>


    </div>
  );
};

export default TermsOfServicePage;
