import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';
import MinimalBlogSidebar from './MinimalBlogSidebar';
import {
  CheckCircleIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentCheckIcon,
  GlobeAltIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ExclamationTriangleIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const EditorialBlogTemplate = ({ post }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedSections, setExpandedSections] = useState({});

  const toggleSection = (sectionId) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Hero Section Component
  const HeroSection = () => (
    <section className="relative py-16 overflow-hidden">
      {/* Subtle Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 to-brand-50/20"></div>
      
      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto text-center"
        >
          {/* Clean Title with Gradient Highlight */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-black text-neutral-900 mb-6 leading-tight">
            How to Get a{' '}
            <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">
              Dummy Ticket
            </span>{' '}
            for Any Visa in 2025
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-neutral-600 mb-8 leading-relaxed font-medium">
            Learn the safest way to provide flight proof for visa applications
          </p>

          {/* Featured Image Placeholder */}
          <div className="w-full h-64 md:h-80 bg-gradient-to-br from-brand-100 to-accent-100 rounded-2xl flex items-center justify-center mb-8 shadow-soft">
            <div className="text-center">
              <div className="text-6xl mb-4">✈️</div>
              <p className="text-brand-600 font-medium">Editorial Travel Image</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Authority Introduction Component
  const AuthorityIntroduction = () => (
    <section className="py-8">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Empathetic Introduction */}
          <div className="prose prose-lg max-w-none mb-8">
            <p className="text-xl text-neutral-700 leading-relaxed mb-6">
              Visa applications can be stressful, especially when you're unsure about booking expensive flights before your visa is approved. Many travelers face the dilemma of needing flight proof without wanting to risk hundreds of dollars on non-refundable tickets.
            </p>
            <p className="text-lg text-neutral-600 leading-relaxed">
              This guide explains everything you need to know about dummy tickets and how to avoid rejections while protecting your finances.
            </p>
          </div>

          {/* Trust Badge Row */}
          <div className="flex flex-wrap justify-center gap-4 py-6 border-t border-b border-neutral-200">
            <div className="flex items-center text-sm text-neutral-600">
              <ShieldCheckIcon className="w-5 h-5 mr-2 text-green-500" />
              <span className="font-medium">Embassy Approved ✅</span>
            </div>
            <div className="flex items-center text-sm text-neutral-600">
              <GlobeAltIcon className="w-5 h-5 mr-2 text-brand-500" />
              <span className="font-medium">75,000+ Travelers 🌍</span>
            </div>
            <div className="flex items-center text-sm text-neutral-600">
              <ClockIcon className="w-5 h-5 mr-2 text-accent-500" />
              <span className="font-medium">Instant Delivery ⏱</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Step-by-Step Guide Component
  const StepByStepGuide = () => {
    const steps = [
      {
        number: 1,
        icon: '🔍',
        title: 'Research Embassy Requirements',
        description: 'Check your destination country\'s specific visa requirements for flight documentation.',
        details: 'Most embassies accept flight reservations or itineraries as proof of travel intent.'
      },
      {
        number: 2,
        icon: '✈️',
        title: 'Choose a Reliable Service',
        description: 'Select a professional dummy ticket service that provides embassy-compliant documents.',
        details: 'Look for services that offer real airline PNRs and verifiable booking references.'
      },
      {
        number: 3,
        icon: '📄',
        title: 'Generate Your Reservation',
        description: 'Provide your travel details and receive your professional flight reservation.',
        details: 'The document should include all required information: passenger details, flight numbers, dates, and booking reference.'
      },
      {
        number: 4,
        icon: '📋',
        title: 'Submit with Your Application',
        description: 'Include the flight reservation with your visa application documents.',
        details: 'Ensure the travel dates align with your intended visa duration and purpose of visit.'
      }
    ];

    return (
      <section className="py-12">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-12 text-center">
              Step-by-Step Guide
            </h2>

            <div className="space-y-8">
              {steps.map((step, index) => (
                <motion.div
                  key={step.number}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start space-x-6 p-6 bg-white rounded-2xl shadow-soft border border-neutral-100"
                >
                  {/* Step Number and Icon */}
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center text-white font-bold text-lg mb-2">
                      {step.number}
                    </div>
                    <div className="text-3xl text-center">{step.icon}</div>
                  </div>

                  {/* Step Content */}
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-neutral-900 mb-3">{step.title}</h3>
                    <p className="text-neutral-700 mb-3 leading-relaxed">{step.description}</p>
                    <p className="text-sm text-neutral-600 leading-relaxed">{step.details}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Pro Tip Box */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mt-12 p-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200"
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircleIcon className="w-5 h-5 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-bold text-green-800 mb-2">✅ Pro Tip</h4>
                  <p className="text-green-700 mb-4 leading-relaxed">
                    Using a professional dummy ticket service ensures embassy compliance and reduces the risk of visa rejection due to improper documentation.
                  </p>
                  <Link
                    to="/"
                    className="inline-flex items-center text-green-600 hover:text-green-700 font-medium text-sm group"
                  >
                    Learn More
                    <ArrowRightIcon className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Mistakes to Avoid Section
  const MistakesToAvoid = () => {
    const mistakes = [
      {
        icon: '❌',
        title: 'Using Free or Unreliable Services',
        description: 'Free dummy ticket generators often produce documents that embassies can easily identify as fake.'
      },
      {
        icon: '⚠️',
        title: 'Inconsistent Travel Dates',
        description: 'Make sure your flight dates align with your visa application dates and intended stay duration.'
      },
      {
        icon: '📝',
        title: 'Missing Essential Information',
        description: 'Ensure your dummy ticket includes passenger name, flight details, booking reference, and airline information.'
      },
      {
        icon: '🕐',
        title: 'Last-Minute Applications',
        description: 'Don\'t wait until the last minute. Some embassies may verify flight reservations with airlines.'
      }
    ];

    return (
      <section className="py-12 bg-neutral-50">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-12 text-center">
              Mistakes to Avoid
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {mistakes.map((mistake, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="p-6 bg-white rounded-xl shadow-soft border border-red-100"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center text-2xl">
                        {mistake.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-neutral-900 mb-2">{mistake.title}</h3>
                      <p className="text-neutral-600 leading-relaxed">{mistake.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Country-Specific Notes Section
  const CountrySpecificNotes = () => {
    const countries = [
      {
        id: 'schengen',
        name: 'Schengen Countries',
        flag: '🇪🇺',
        notes: [
          'Flight reservations are explicitly accepted for Schengen visa applications',
          'Must show entry and exit from Schengen area',
          'Dates should align with your intended stay duration',
          'Some consulates may verify reservations with airlines'
        ]
      },
      {
        id: 'usa',
        name: 'United States',
        flag: '🇺🇸',
        notes: [
          'Flight itineraries are recommended but not always required',
          'Focus on demonstrating strong ties to home country',
          'Round-trip reservations show intent to return',
          'B1/B2 visas: itinerary helps establish travel purpose'
        ]
      },
      {
        id: 'canada',
        name: 'Canada',
        flag: '🇨🇦',
        notes: [
          'Flight reservations accepted for visitor visa applications',
          'Must demonstrate sufficient funds for travel',
          'Include detailed travel itinerary with reservations',
          'Temporary resident visa: show planned departure'
        ]
      },
      {
        id: 'uk',
        name: 'United Kingdom',
        flag: '🇬🇧',
        notes: [
          'Flight bookings help demonstrate genuine visit intention',
          'Standard visitor visa: show planned return journey',
          'Include accommodation bookings alongside flight reservations',
          'Transit visas: confirmed onward travel required'
        ]
      }
    ];

    return (
      <section className="py-12 bg-neutral-50">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-12 text-center">
              Country-Specific Requirements
            </h2>

            <div className="space-y-4">
              {countries.map((country, index) => (
                <motion.div
                  key={country.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-soft border border-neutral-200 overflow-hidden"
                >
                  <button
                    onClick={() => toggleSection(country.id)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <span className="text-3xl">{country.flag}</span>
                      <h3 className="text-xl font-bold text-neutral-900">{country.name}</h3>
                    </div>
                    <div className="text-neutral-400">
                      {expandedSections[country.id] ? (
                        <ChevronUpIcon className="w-6 h-6" />
                      ) : (
                        <ChevronDownIcon className="w-6 h-6" />
                      )}
                    </div>
                  </button>

                  {expandedSections[country.id] && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="px-6 pb-6"
                    >
                      <div className="border-t border-neutral-200 pt-4">
                        <ul className="space-y-3">
                          {country.notes.map((note, noteIndex) => (
                            <li key={noteIndex} className="flex items-start space-x-3">
                              <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-neutral-700 leading-relaxed">{note}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Natural Product Mention Section
  const NaturalProductMention = () => (
    <section className="py-12">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-neutral-700 leading-relaxed mb-6">
              Many travelers now use embassy-approved services like VerifiedOnward to avoid costly rejections.
              These services generate authentic-looking reservations instantly, providing the documentation
              embassies expect while protecting your financial investment.
            </p>

            <div className="not-prose">
              <Link
                to="/"
                className="inline-flex items-center px-6 py-3 bg-brand-50 hover:bg-brand-100 text-brand-700 font-medium rounded-xl border border-brand-200 transition-colors duration-200 group"
              >
                Get a Secure Reservation
                <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Embedded Testimonial Section
  const EmbeddedTestimonial = () => (
    <section className="py-12">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-3xl mx-auto"
        >
          <blockquote className="relative p-8 bg-gradient-to-r from-brand-50/50 to-accent-50/50 rounded-2xl border-l-4 border-brand-400">
            <div className="absolute top-4 left-4 text-4xl text-brand-300 opacity-50">"</div>
            <p className="text-xl text-neutral-700 leading-relaxed mb-6 pl-8">
              I used VerifiedOnward for my Schengen visa and got approved without issues.
              The document looked professional and the embassy accepted it immediately.
              Saved me over €300 compared to booking a real flight!
            </p>
            <footer className="flex items-center pl-8">
              <div className="w-12 h-12 bg-brand-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                S
              </div>
              <div>
                <cite className="text-neutral-900 font-semibold not-italic">Sarah M.</cite>
                <p className="text-neutral-600 text-sm">UK → Spain</p>
              </div>
            </footer>
          </blockquote>
        </motion.div>
      </div>
    </section>
  );

  // Trust Section
  const TrustSection = () => {
    const trustPoints = [
      {
        icon: <ShieldCheckIcon className="w-6 h-6" />,
        title: 'Embassy-Approved Documents',
        description: 'Professional format accepted by embassies worldwide'
      },
      {
        icon: <ClockIcon className="w-6 h-6" />,
        title: '60s Instant Delivery',
        description: 'Get your reservation immediately via email'
      },
      {
        icon: <DocumentCheckIcon className="w-6 h-6" />,
        title: '$4.99 Fixed Price',
        description: 'Transparent pricing with no hidden fees'
      }
    ];

    return (
      <section className="py-16 bg-gradient-to-br from-neutral-50 to-brand-50/30">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Why Choose VerifiedOnward?
            </h2>
            <p className="text-lg text-neutral-600 mb-12 max-w-2xl mx-auto">
              Trusted by thousands of travelers for reliable, embassy-compliant flight reservations
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {trustPoints.map((point, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="p-6 bg-white rounded-xl shadow-soft"
                >
                  <div className="w-12 h-12 bg-brand-100 rounded-full flex items-center justify-center text-brand-600 mx-auto mb-4">
                    {point.icon}
                  </div>
                  <h3 className="text-lg font-bold text-neutral-900 mb-2">{point.title}</h3>
                  <p className="text-neutral-600">{point.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Final Conversion CTA
  const FinalConversionCTA = () => (
    <section className="py-16">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="relative p-12 bg-gradient-to-br from-brand-500/5 via-white to-brand-300/5 rounded-3xl border border-brand-100 overflow-hidden">
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-[0.02]" style={{
              backgroundImage: `radial-gradient(circle at 2px 2px, rgb(14 165 233) 1px, transparent 0)`,
              backgroundSize: '40px 40px'
            }}></div>

            <div className="relative z-10 text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
                When you're ready to apply, we're here to help.
              </h2>
              <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                Trusted by 75,000+ travelers. Instant, secure, and embassy-compliant flight reservations
                that protect your investment while meeting visa requirements.
              </p>

              <Link
                {...getCTANavigationProps(navigate, location.pathname)}
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white text-lg font-bold rounded-2xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 group"
              >
                Generate Your Flight Ticket Now
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>

              <div className="mt-6 text-sm text-neutral-500">
                ✓ 99.8% Embassy Acceptance • ✓ Instant Download • ✓ Secure Payment
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  return (
    <div className="min-h-screen bg-white relative">
      {/* Minimal Sidebar */}
      <MinimalBlogSidebar />

      {/* Main Content */}
      <HeroSection />
      <AuthorityIntroduction />
      <StepByStepGuide />
      <CountrySpecificNotes />
      <MistakesToAvoid />
      <NaturalProductMention />
      <EmbeddedTestimonial />
      <TrustSection />
      <FinalConversionCTA />
    </div>
  );
};

export default EditorialBlogTemplate;
