@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import 'react-toastify/dist/ReactToastify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Base Styles */
@layer base {
  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Premium Aviation Component Styles */
@layer components {
  /* Premium Aviation Button Styles */
  .btn-primary {
    @apply bg-brand-500 hover:bg-brand-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-white hover:bg-brand-50 text-brand-600 font-semibold px-6 py-3 rounded-xl border border-brand-200 transition-all duration-300 shadow-soft hover:shadow-aviation focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-gold hover:shadow-accent-glow focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 transform hover:scale-105;
  }

  /* Enhanced CTA Button System - Premium Consistency */
  .btn-cta-primary {
    @apply bg-gradient-to-r from-brand-500 via-brand-600 to-brand-700 hover:from-brand-600 hover:via-brand-700 hover:to-brand-800 text-white font-bold px-8 py-4 rounded-2xl transition-all duration-300 shadow-aviation hover:shadow-aviation-hover focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-[1.05] hover:-translate-y-1 active:scale-[0.98] relative overflow-hidden;
    /* Enhanced visual consistency with subtle glow */
    border: 1px solid rgba(14, 165, 233, 0.2);
    backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.2), 0 0 0 1px rgba(14, 165, 233, 0.05), 0 0 20px rgba(14, 165, 233, 0.1);
  }

  .btn-cta-primary:hover {
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.3), 0 0 0 1px rgba(14, 165, 233, 0.1), 0 0 30px rgba(14, 165, 233, 0.2);
  }

  .btn-cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-cta-primary:hover::before {
    left: 100%;
  }

  .btn-cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
  }

  .btn-cta-primary:hover::before {
    left: 100%;
  }

  .btn-cta-secondary {
    @apply bg-neutral-700 hover:bg-neutral-800 text-white font-bold px-8 py-4 rounded-2xl border-2 border-neutral-600 hover:border-neutral-700 transition-all duration-300 shadow-soft hover:shadow-aviation focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98];
    backdrop-filter: blur(8px);
  }

  /* Learn More button style with high contrast */
  .btn-learn-more {
    @apply bg-neutral-800 hover:bg-neutral-900 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-medium hover:shadow-strong focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2 transform hover:scale-[1.02] hover:-translate-y-0.5 active:scale-[0.98];
  }

  .btn-cta-large {
    @apply px-12 py-6 text-lg md:text-xl rounded-3xl;
  }

  .btn-cta-small {
    @apply px-6 py-3 text-sm rounded-xl;
  }

  /* Floating CTA consistency with enhanced visibility */
  .btn-cta-floating {
    @apply btn-cta-primary shadow-luxury hover:shadow-glow;
    border-radius: 1rem; /* 16px - consistent with design system */
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05), 0 0 30px rgba(14, 165, 233, 0.15);
    backdrop-filter: blur(12px);
  }

  .btn-cta-floating:hover {
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35), 0 0 0 1px rgba(255, 255, 255, 0.1), 0 0 40px rgba(14, 165, 233, 0.25);
    transform: scale(1.05) translateY(-2px);
  }

  /* Premium Aviation CTA Button (Legacy Support) */
  .btn-aviation-primary {
    @apply btn-cta-primary;
  }

  /* Premium Aviation Form Elements */
  .input-modern {
    @apply w-full px-4 py-3 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-all duration-300 bg-white placeholder-neutral-400 text-neutral-900;
  }

  .input-modern:focus {
    @apply shadow-aviation;
  }

  .input-aviation {
    @apply w-full px-5 py-4 border-2 border-neutral-200 rounded-2xl focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-all duration-300 bg-white placeholder-neutral-400 text-neutral-900 font-medium shadow-soft hover:shadow-aviation;
  }

  /* Date Input Specific Styles */
  .input-modern[type="date"] {
    @apply cursor-pointer;
    position: relative;
    z-index: 1;
  }

  .input-modern[type="date"]::-webkit-calendar-picker-indicator {
    @apply cursor-pointer opacity-100;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    z-index: 2;
  }

  .input-modern[type="date"]::-webkit-inner-spin-button,
  .input-modern[type="date"]::-webkit-clear-button {
    display: none;
    -webkit-appearance: none;
  }

  /* Premium Aviation Card Styles */
  .card-modern {
    @apply bg-white rounded-2xl shadow-soft border border-neutral-100 p-6 transition-all duration-300;
  }

  .card-modern:hover {
    @apply shadow-aviation transform hover:-translate-y-1;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-aviation border border-neutral-100 p-8;
  }

  .card-aviation {
    @apply bg-white rounded-3xl shadow-aviation border border-brand-100 p-8 transition-all duration-300 hover:shadow-aviation-hover hover:-translate-y-2;
    /* Ensure shadow is not clipped */
    margin: 8px;
  }

  .card-premium {
    @apply bg-gradient-to-br from-white to-brand-50/30 rounded-3xl shadow-luxury border border-brand-100/50 p-8 backdrop-blur-sm;
  }

  /* Enhanced Typography Hierarchy for Better Readability */
  .heading-hero {
    @apply text-5xl md:text-6xl lg:text-7xl font-black text-neutral-900 leading-[1.1] tracking-tight;
  }

  .heading-primary {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 leading-tight tracking-tight;
  }

  .heading-secondary {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold text-neutral-800 leading-tight tracking-tight;
  }

  .heading-tertiary {
    @apply text-xl md:text-2xl lg:text-3xl font-semibold text-neutral-800 leading-snug;
  }

  /* Enhanced body text for better readability */
  .text-body {
    @apply text-base md:text-lg leading-relaxed text-neutral-700 font-normal;
    font-size: 16px;
    line-height: 1.6;
  }

  .text-body-large {
    @apply text-lg md:text-xl leading-relaxed text-neutral-700 font-normal;
    line-height: 1.6;
  }

  .text-subheading {
    @apply text-lg md:text-xl font-medium text-neutral-600 leading-relaxed;
    line-height: 1.5;
  }

  .heading-secondary {
    @apply text-2xl md:text-3xl font-semibold text-neutral-800 leading-tight;
  }

  .heading-tertiary {
    @apply text-xl md:text-2xl font-semibold text-neutral-800 leading-tight;
  }

  .heading-quaternary {
    @apply text-lg md:text-xl font-semibold text-neutral-700 leading-tight;
  }

  .text-body {
    @apply text-base text-neutral-600 leading-relaxed font-normal;
  }

  .text-body-large {
    @apply text-lg text-neutral-600 leading-relaxed font-normal;
  }

  .text-body-small {
    @apply text-sm text-neutral-600 leading-relaxed font-normal;
  }

  /* Enhanced Gradient Text Effects - Premium Contrast & Legibility */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-brand-800 via-brand-700 to-brand-600 bg-clip-text text-transparent;
    /* Fallback for better accessibility */
    color: #075985;
    text-shadow: 0 1px 2px rgba(7, 89, 133, 0.1);
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-800 via-accent-700 to-accent-600 bg-clip-text text-transparent;
    /* Fallback for better accessibility */
    color: #854d0e;
    text-shadow: 0 1px 2px rgba(133, 77, 14, 0.1);
  }

  .text-gradient-subtle {
    @apply bg-gradient-to-r from-brand-700 via-brand-600 to-accent-700 bg-clip-text text-transparent;
    /* Enhanced contrast with stronger color stops */
    color: #0369a1;
    text-shadow: 0 1px 3px rgba(3, 105, 161, 0.15);
  }

  /* Premium gradient text with enhanced readability */
  .text-gradient-hero {
    @apply bg-gradient-to-r from-brand-800 via-brand-700 to-brand-600 bg-clip-text text-transparent;
    color: #075985;
    text-shadow: 0 2px 4px rgba(7, 89, 133, 0.12);
    /* Ensure proper contrast ratio */
    background-size: 200% 200%;
    animation: gradientShift 8s ease-in-out infinite;
  }

  /* Accessibility-first gradient text */
  .text-gradient-accessible {
    @apply bg-gradient-to-r from-brand-900 via-brand-800 to-brand-700 bg-clip-text text-transparent;
    color: #0c4a6e;
    text-shadow: 0 1px 2px rgba(12, 74, 110, 0.2);
  }

  /* Enhanced Spacing Utilities */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  /* Professional Sections */
  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-modern {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Modern CTA Styles */
.cta-box {
  @apply bg-gradient-to-br from-brand-50 to-accent-50 border-2 border-brand-100 shadow-soft rounded-2xl;
}

.cta-button {
  @apply btn-primary transform hover:scale-105;
}

/* Modern Flight Search Enhancements */
.flight-inputs-container {
  @apply w-full;
}

.airport-input-enhanced {
  @apply relative;
}

.airport-tooltip {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 bg-neutral-900 text-white px-3 py-2 rounded-lg text-xs whitespace-nowrap z-50 opacity-0 pointer-events-none transition-opacity duration-200 mb-2;
}

.airport-tooltip::after {
  @apply absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-neutral-900;
  content: '';
}

.airport-input-enhanced:hover .airport-tooltip {
  @apply opacity-100;
}

/* Modern Responsive Enhancements */
@layer utilities {
  .airport-input-wide {
    @apply md:min-w-[250px] lg:min-w-[280px] xl:min-w-[320px];
  }

  .airport-input-mobile {
    @apply min-h-[48px] text-base; /* Prevents zoom on iOS */
  }

  /* Professional hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-medium;
  }

  /* Focus states */
  .focus-modern {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2;
  }

  /* Touch targets for mobile accessibility */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* Consistent Vertical Rhythm System */
  .section-rhythm {
    padding-top: 5rem; /* 80px */
    padding-bottom: 5rem; /* 80px */
  }

  .section-rhythm-large {
    padding-top: 6rem; /* 96px */
    padding-bottom: 6rem; /* 96px */
  }

  .section-rhythm-small {
    padding-top: 3rem; /* 48px */
    padding-bottom: 3rem; /* 48px */
  }

  /* Extra small screen optimizations */
  @media (min-width: 475px) {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
  }

  /* Premium Aviation Design Elements */
  .aviation-gradient {
    background: linear-gradient(135deg, #0ea5e9 0%, #eab308 100%);
    background-size: 200% 200%;
  }

  .aviation-gradient-hero {
    background: linear-gradient(135deg, #f0f9ff 0%, #fefce8 50%, #e0f2fe 100%);
  }

  .gradient-luxury-hero {
    background: linear-gradient(135deg, #f0f9ff 0%, #fefce8 50%, #e0f2fe 100%);
  }

  .animate-luxury-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-luxury-pulse {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  /* Main Shadow System - Aviation Theme */
  .shadow-soft {
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
  }

  .shadow-aviation {
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.25), 0 0 0 1px rgba(14, 165, 233, 0.05);
  }

  .shadow-aviation-hover {
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.35), 0 8px 16px -4px rgba(14, 165, 233, 0.2), 0 0 0 1px rgba(14, 165, 233, 0.1);
  }

  .shadow-pricing-deep {
    box-shadow: 0 12px 40px -8px rgba(14, 165, 233, 0.3), 0 4px 12px -2px rgba(14, 165, 233, 0.15), 0 0 0 1px rgba(14, 165, 233, 0.08);
  }

  .shadow-pricing-deep-hover {
    box-shadow: 0 24px 64px -12px rgba(14, 165, 233, 0.4), 0 12px 24px -6px rgba(14, 165, 233, 0.25), 0 0 0 1px rgba(14, 165, 233, 0.15);
  }

  .shadow-glow {
    box-shadow: 0 4px 20px -4px rgba(14, 165, 233, 0.3);
  }

  .shadow-luxury {
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.4), 0 0 0 1px rgba(14, 165, 233, 0.1);
  }

  .premium-card {
    @apply bg-white rounded-3xl shadow-luxury border border-brand-100/50 backdrop-blur-sm;
  }

  .premium-button {
    @apply btn-cta-primary;
  }

  .premium-button-large {
    @apply btn-cta-primary btn-cta-large;
  }

  /* Premium Trust Badge System - Harmonized Visual Weight */
  .trust-badge {
    @apply inline-flex items-center px-4 py-2 bg-brand-50 text-brand-700 rounded-full text-sm font-medium border border-brand-200 shadow-brand-glow transition-all duration-300 hover:bg-brand-100;
    /* Standardized icon sizing */
    svg {
      @apply w-5 h-5 mr-2 text-brand-600;
      filter: saturate(1.1) brightness(1.05);
    }
  }

  .trust-badge-gold {
    @apply inline-flex items-center px-4 py-2 bg-accent-50 text-accent-700 rounded-full text-sm font-medium border border-accent-200 shadow-accent-glow transition-all duration-300 hover:bg-accent-100;
    /* Standardized icon sizing */
    svg {
      @apply w-5 h-5 mr-2 text-accent-600;
      filter: saturate(1.1) brightness(1.05);
    }
  }

  /* Premium Trust Badges for Price Sections */
  .trust-badge-ssl {
    @apply inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 rounded-full text-xs font-medium border border-green-200 transition-all duration-300 hover:bg-green-100;
  }

  .trust-badge-embassy {
    @apply inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded-full text-xs font-medium border border-blue-200 transition-all duration-300 hover:bg-blue-100;
  }

  .trust-badge-instant {
    @apply inline-flex items-center px-3 py-1.5 bg-orange-50 text-orange-700 rounded-full text-xs font-medium border border-orange-200 transition-all duration-300 hover:bg-orange-100;
  }

  /* Best Value Guarantee Badge */
  .value-guarantee-badge {
    @apply inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full text-xs font-bold shadow-sm;
    box-shadow: 0 2px 8px -2px rgba(34, 197, 94, 0.3);
  }

  /* Price Section Container */
  .price-section {
    @apply relative bg-white rounded-2xl p-6 border border-neutral-100 shadow-soft transition-all duration-300 hover:shadow-aviation;
  }

  .price-section-premium {
    @apply price-section;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.8) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 87, 184, 0.1);
  }

  .trust-badge-green {
    @apply inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium border border-green-200 shadow-success-glow transition-all duration-300 hover:bg-green-100;
    /* Standardized icon sizing */
    svg {
      @apply w-5 h-5 mr-2 text-green-600;
      filter: saturate(1.1) brightness(1.05);
    }
  }

  .trust-badge-purple {
    @apply inline-flex items-center px-4 py-2 bg-purple-50 text-purple-700 rounded-full text-sm font-medium border border-purple-200 transition-all duration-300 hover:bg-purple-100;
    /* Standardized icon sizing */
    svg {
      @apply w-5 h-5 mr-2 text-purple-600;
      filter: saturate(1.1) brightness(1.05);
    }
  }

  .urgency-indicator {
    @apply inline-flex items-center px-3 py-1 bg-red-50 text-red-700 rounded-full text-xs font-medium border border-red-200 animate-pulse-glow;
  }

  /* Harmonized Icon System */
  .icon-standard {
    @apply w-5 h-5;
    filter: saturate(1.1) brightness(1.05);
    transition: all 0.2s ease;
  }

  .icon-large {
    @apply w-6 h-6;
    filter: saturate(1.1) brightness(1.05);
    transition: all 0.2s ease;
  }

  .icon-feature {
    @apply w-12 h-12;
    filter: saturate(1.15) brightness(1.1);
    transition: all 0.3s ease;
  }

  .icon-metric {
    @apply w-10 h-10;
    filter: saturate(1.2) brightness(1.1);
    transition: all 0.2s ease;
  }

  /* Icon hover effects */
  .icon-standard:hover,
  .icon-large:hover,
  .icon-feature:hover,
  .icon-metric:hover {
    filter: saturate(1.3) brightness(1.2);
    transform: scale(1.05);
  }

  /* Enhanced Form Styling */
  .form-section {
    @apply bg-white rounded-3xl shadow-aviation border border-brand-100/50 p-8 md:p-12 relative overflow-hidden;
  }

  .form-input-enhanced {
    @apply w-full px-4 py-3 border-2 border-neutral-200 rounded-xl focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20 transition-all duration-300 text-base font-medium placeholder-neutral-400;
  }

  .form-label-enhanced {
    @apply block text-sm font-semibold text-neutral-700 mb-2;
  }

  .form-helper-text {
    @apply text-xs text-neutral-500 mt-1;
  }

  /* Premium Price Styling System - Consistent Blue-to-Green Gradient */
  .price-highlight {
    @apply text-4xl md:text-5xl font-bold;
    background: linear-gradient(90deg, #0057B8 0%, #00C896 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0057B8; /* Fallback for accessibility */
    text-shadow: 0 2px 4px rgba(0, 87, 184, 0.1);
  }

  /* Premium Price Pill - Main pricing display component */
  .price-pill {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-bold text-white transition-all duration-300;
    background: linear-gradient(90deg, #0057B8 0%, #00C896 100%);
    box-shadow: 0 4px 16px -4px rgba(0, 87, 184, 0.3), 0 0 0 1px rgba(0, 200, 150, 0.1);
    transform: translateY(0);
  }

  .price-pill:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 24px -4px rgba(0, 87, 184, 0.4), 0 4px 12px -2px rgba(0, 200, 150, 0.2);
  }

  /* Large Price Pill for prominent displays */
  .price-pill-large {
    @apply price-pill text-lg md:text-xl px-6 py-3 rounded-xl;
    font-size: 18px;
    min-height: 48px;
  }

  /* Small Price Pill for compact displays */
  .price-pill-small {
    @apply price-pill text-sm px-3 py-1.5 rounded-lg;
    font-size: 14px;
  }

  /* Premium Price Text - For text-only price displays */
  .price-premium {
    @apply font-bold;
    background: linear-gradient(90deg, #0057B8 0%, #00C896 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0057B8; /* Fallback */
    filter: saturate(1.1) brightness(1.05);
  }

  /* Price with subtle glow effect */
  .price-glow {
    @apply price-premium;
    text-shadow: 0 0 8px rgba(0, 87, 184, 0.2);
  }

  .aviation-hero-text {
    @apply text-5xl md:text-7xl font-black bg-gradient-to-r from-brand-700 via-brand-600 to-brand-500 bg-clip-text text-transparent leading-tight;
  }

  .shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Premium Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes scroll-horizontal {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
  }

  .animate-scroll-horizontal {
    animation: scroll-horizontal 20s linear infinite;
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(249, 115, 22, 0.5); }
    50% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.8), 0 0 30px rgba(249, 115, 22, 0.4); }
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    0% {
      opacity: 0;
      transform: translateX(-30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes countUp {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  .animate-count-up {
    animation: countUp 0.8s ease-out forwards;
  }

  /* Premium Shadow Effects */
  .shadow-luxury {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-glow {
    box-shadow: 0 10px 25px -5px rgba(30, 41, 59, 0.3), 0 10px 10px -5px rgba(30, 41, 59, 0.04);
  }

  .shadow-success-glow {
    box-shadow: 0 10px 25px -5px rgba(249, 115, 22, 0.3), 0 10px 10px -5px rgba(249, 115, 22, 0.04);
  }

  /* Premium Aviation Animation Utilities */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px -10px rgba(14, 165, 233, 0.2);
  }

  .hover-aviation {
    transition: all 0.3s ease;
  }

  .hover-aviation:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.3);
  }

  .aviation-shimmer {
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.4), rgba(234, 179, 8, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 3s infinite;
  }

  .animate-aviation-slide {
    animation: aviation-slide 3s ease-in-out infinite;
  }

  .animate-aviation-glow {
    animation: aviation-glow 3s ease-in-out infinite;
  }

  /* Enhanced Mobile Optimizations */
  @media (max-width: 768px) {
    .heading-hero {
      font-size: 2.5rem;
      line-height: 1.1;
    }

    .heading-primary {
      font-size: 2rem;
      line-height: 1.2;
    }

    .heading-secondary {
      font-size: 1.5rem;
      line-height: 1.3;
    }

    .card-aviation {
      padding: 1.5rem;
      margin-bottom: 2rem; /* Better vertical spacing */
    }

    .btn-cta-primary, .btn-aviation-primary {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
      min-height: 48px; /* Thumb-friendly touch targets */
      min-width: 48px;
    }

    .btn-cta-large {
      padding: 1rem 2rem;
      font-size: 1.125rem;
      min-height: 52px; /* Larger touch targets for primary actions */
    }

    .input-aviation {
      padding: 0.875rem 1rem;
      font-size: 1rem;
      min-height: 48px; /* Better touch targets */
    }

    .container-modern {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .trust-badge, .trust-badge-gold, .trust-badge-green, .trust-badge-purple {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
      flex-shrink: 0;
    }

    /* Sticky CTA for mobile */
    .btn-cta-floating {
      bottom: 1rem;
      right: 1rem;
      left: 1rem;
      width: auto;
      position: fixed;
      z-index: 50;
    }

    /* Better card spacing on mobile */
    .use-case-card {
      margin-bottom: 1.5rem;
    }

    .form-section {
      padding: 1.5rem;
    }

    /* Mobile badge scrolling optimization */
    .animate-scroll-horizontal {
      animation-duration: 15s; /* Slower on mobile for better readability */
    }

    /* Mobile section rhythm adjustments */
    .section-rhythm {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }

    .section-rhythm-large {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
  }

  @media (max-width: 640px) {
    .heading-hero {
      font-size: 2rem;
      line-height: 1.1;
    }

    .heading-primary {
      font-size: 1.75rem;
      line-height: 1.2;
    }

    .price-highlight {
      font-size: 2rem;
    }

    .price-pill-large {
      font-size: 16px;
      padding: 0.75rem 1.25rem;
    }

    .price-pill-small {
      font-size: 12px;
      padding: 0.5rem 0.75rem;
    }

    .trust-badge-ssl,
    .trust-badge-embassy,
    .trust-badge-instant {
      font-size: 0.625rem;
      padding: 0.25rem 0.5rem;
    }

    .card-aviation {
      padding: 1rem;
      border-radius: 1.5rem;
    }

    .shadow-aviation {
      box-shadow: 0 6px 24px -6px rgba(14, 165, 233, 0.2), 0 0 0 1px rgba(14, 165, 233, 0.05);
    }

    .btn-cta-primary, .btn-aviation-primary {
      padding: 0.75rem 1.25rem;
      font-size: 0.875rem;
    }

    .btn-cta-large {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }

    .trust-badge, .trust-badge-gold, .trust-badge-green, .trust-badge-purple {
      padding: 0.375rem 0.625rem;
      font-size: 0.6875rem;
    }

    /* Improved touch targets for mobile */
    .touch-target {
      min-width: 48px;
      min-height: 48px;
    }

    /* Better spacing on small screens */
    .section-spacing {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .hover-aviation:hover {
      transform: none;
    }

    .btn-aviation-primary:active {
      transform: scale(0.95);
    }

    .card-aviation:active {
      transform: scale(0.98);
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .shadow-aviation {
      box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.25);
    }
  }

  /* Performance optimizations */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-bounce-subtle,
    .animate-pulse-glow,
    .animate-shimmer,
    .animate-aviation-slide,
    .animate-aviation-glow {
      animation: none;
    }

    .hover-aviation:hover,
    .hover-lift:hover {
      transform: none;
    }
  }

  /* Accessibility improvements */
  @media (prefers-contrast: high) {
    .shadow-aviation {
      box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.4);
    }

    .text-brand-600 {
      color: #0369a1;
    }

    .text-accent-600 {
      color: #ca8a04;
    }
  }

  /* Print styles */
  @media print {
    .shadow-aviation,
    .shadow-aviation-hover,
    .shadow-luxury {
      box-shadow: none;
    }

    .bg-gradient-to-r,
    .bg-gradient-to-br,
    .aviation-gradient-hero {
      background: white !important;
    }

    .text-white {
      color: black !important;
    }
  }

  /* Accessibility styles */
  .skip-links {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
  }

  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus styles for keyboard navigation */
  .keyboard-navigation *:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
  }

  /* Tooltip styles */
  .tooltip {
    position: absolute;
    background: #1f2937;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000;
    max-width: 200px;
    word-wrap: break-word;
  }

  /* £20,000+ Premium Design System Enhancements */

  /* Advanced Glassmorphism Effects */
  .glass-luxury {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-aviation {
    background: rgba(14, 165, 233, 0.1);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(14, 165, 233, 0.2);
    box-shadow: 0 20px 40px -10px rgba(14, 165, 233, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-accent {
    background: rgba(234, 179, 8, 0.1);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(234, 179, 8, 0.2);
    box-shadow: 0 20px 40px -10px rgba(234, 179, 8, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Luxury Typography Hierarchy */
  .text-luxury-display {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
  }

  .text-luxury-display-stable {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
    color: #1e293b;
  }

  .text-luxury-hero {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.01em;
    background: linear-gradient(135deg, #1e293b 0%, #0ea5e9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-luxury-title {
    font-size: clamp(1.875rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.01em;
    color: #1e293b;
  }

  .text-luxury-subtitle {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 600;
    line-height: 1.4;
    color: #475569;
  }

  /* Premium Shadow System */
  .shadow-luxury-soft {
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1),
                0 2px 8px -2px rgba(14, 165, 233, 0.05);
  }

  .shadow-luxury-medium {
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.2),
                0 4px 16px -4px rgba(14, 165, 233, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-luxury-strong {
    box-shadow: 0 16px 64px -16px rgba(14, 165, 233, 0.3),
                0 8px 32px -8px rgba(14, 165, 233, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .shadow-luxury-glow {
    box-shadow: 0 0 40px rgba(14, 165, 233, 0.4),
                0 16px 64px -16px rgba(14, 165, 233, 0.3),
                0 8px 32px -8px rgba(14, 165, 233, 0.2);
  }

  /* Advanced Gradient System */
  .gradient-luxury-primary {
    background: linear-gradient(135deg,
      #0ea5e9 0%,
      #0284c7 25%,
      #0369a1 50%,
      #075985 75%,
      #0c4a6e 100%);
  }

  .gradient-luxury-accent {
    background: linear-gradient(135deg,
      #eab308 0%,
      #d97706 25%,
      #dc2626 50%,
      #b91c1c 75%,
      #991b1b 100%);
  }

  .gradient-luxury-hero {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(59, 130, 246, 0.05) 25%,
      rgba(147, 197, 253, 0.1) 50%,
      rgba(234, 179, 8, 0.05) 75%,
      rgba(251, 191, 36, 0.1) 100%);
  }

  /* Premium Animation Keyframes */
  @keyframes luxury-float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.8;
    }
    25% {
      transform: translateY(-10px) rotate(1deg);
      opacity: 0.9;
    }
    50% {
      transform: translateY(-20px) rotate(0deg);
      opacity: 1;
    }
    75% {
      transform: translateY(-10px) rotate(-1deg);
      opacity: 0.9;
    }
  }

  @keyframes luxury-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 40px rgba(14, 165, 233, 0.6);
    }
  }

  @keyframes luxury-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes luxury-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(14, 165, 233, 0.3),
                  0 0 40px rgba(14, 165, 233, 0.1);
    }
    50% {
      box-shadow: 0 0 40px rgba(14, 165, 233, 0.6),
                  0 0 80px rgba(14, 165, 233, 0.3);
    }
  }

  /* Professional Text Effects */
  @keyframes subtle-glow {
    0%, 100% {
      text-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
    }
    50% {
      text-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
    }
  }

  .text-professional-glow {
    animation: subtle-glow 4s ease-in-out infinite;
  }

  /* Premium Animation Classes */
  .animate-luxury-float {
    animation: luxury-float 8s ease-in-out infinite;
  }

  .animate-luxury-pulse {
    animation: luxury-pulse 3s ease-in-out infinite;
  }

  .animate-luxury-shimmer {
    position: relative;
    overflow: hidden;
  }

  .animate-luxury-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    animation: luxury-shimmer 6s infinite;
    pointer-events: none;
  }

  .animate-luxury-glow {
    animation: luxury-glow 4s ease-in-out infinite;
  }

  /* How It Works Page Premium Animations */
  @keyframes step-connect-line {
    0% {
      width: 0%;
      opacity: 0;
    }
    100% {
      width: 100%;
      opacity: 1;
    }
  }

  @keyframes checkmark-pop {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes trust-badge-scale {
    0% {
      transform: scale(0.9);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes cta-pulse-soft {
    0%, 100% {
      box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.3);
    }
    50% {
      box-shadow: 0 12px 40px -8px rgba(14, 165, 233, 0.5);
    }
  }

  @keyframes step-card-slide-in {
    0% {
      opacity: 0;
      transform: translateX(-30px) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0) translateY(0);
    }
  }

  @keyframes benefit-cascade {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* How It Works Animation Classes */
  .animate-step-connect {
    animation: step-connect-line 1.5s ease-out forwards;
  }

  .animate-checkmark-pop {
    animation: checkmark-pop 0.3s ease-out;
  }

  .animate-trust-badge-scale {
    animation: trust-badge-scale 0.6s ease-out forwards;
  }

  .animate-cta-pulse-soft {
    animation: cta-pulse-soft 8s ease-in-out infinite;
  }

  .animate-step-card-slide {
    animation: step-card-slide-in 0.8s ease-out forwards;
  }

  .animate-benefit-cascade {
    animation: benefit-cascade 0.5s ease-out forwards;
  }

  /* Step Connection Line Styles */
  .step-connector {
    position: relative;
  }

  .step-connector::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -50%;
    width: 0%;
    height: 2px;
    background: linear-gradient(90deg, rgba(14, 165, 233, 0.3), rgba(14, 165, 233, 0.1));
    transform: translateY(-50%);
    z-index: 1;
  }

  .step-connector.animate::after {
    animation: step-connect-line 1.5s ease-out 0.5s forwards;
  }

  /* Enhanced Hover Effects for How It Works */
  .step-card-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .step-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px -12px rgba(14, 165, 233, 0.25);
  }

  .trust-badge-enhanced {
    transition: all 0.3s ease;
  }

  .trust-badge-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(34, 197, 94, 0.4);
    filter: brightness(1.05);
  }

  .checkmark-enhanced {
    transition: all 0.2s ease;
  }

  .checkmark-enhanced:hover {
    transform: scale(1.1);
  }

  /* Section Background Enhancements */
  .why-use-section-bg {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.02) 0%,
      rgba(251, 191, 36, 0.02) 50%,
      rgba(14, 165, 233, 0.02) 100%);
  }

  .footer-transition-gradient {
    background: linear-gradient(180deg,
      rgba(14, 165, 233, 0.05) 0%,
      rgba(251, 191, 36, 0.03) 50%,
      transparent 100%);
    height: 80px;
  }

  /* FAQ Page Premium Styles */
  .faq-search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
  }

  .faq-search-input {
    @apply w-full px-6 py-4 pl-14 pr-12 text-lg bg-white/90 backdrop-blur-md border-2 border-brand-200 rounded-2xl shadow-aviation focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 focus:outline-none transition-all duration-300;
  }

  .faq-search-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px -8px rgba(14, 165, 233, 0.3);
  }

  .faq-search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    transition: color 0.3s ease;
  }

  .faq-search-input:focus + .faq-search-icon {
    color: #0ea5e9;
  }

  .faq-clear-button {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    @apply w-8 h-8 bg-neutral-200 hover:bg-neutral-300 rounded-full flex items-center justify-center transition-all duration-200 cursor-pointer;
  }

  .faq-category-badge {
    @apply inline-flex items-center px-3 py-1 bg-gradient-to-r from-brand-100 to-accent-100 text-brand-700 rounded-full text-sm font-semibold border border-brand-200/50;
  }

  .faq-accordion-item {
    @apply bg-gradient-to-br from-white via-white to-brand-50/30 rounded-2xl shadow-aviation border border-brand-100/50 overflow-hidden transition-all duration-300 hover:shadow-aviation-hover hover:-translate-y-1;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.98) 50%,
      rgba(14, 165, 233, 0.02) 100%);
  }

  .faq-accordion-button {
    @apply w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gradient-to-r hover:from-brand-50/50 hover:to-accent-50/50 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:ring-inset;
  }

  .faq-accordion-button:hover {
    background: linear-gradient(90deg,
      rgba(14, 165, 233, 0.03) 0%,
      rgba(251, 191, 36, 0.02) 100%);
  }

  .faq-accordion-icon {
    @apply w-10 h-10 bg-gradient-to-r from-brand-500 to-accent-500 rounded-xl flex items-center justify-center shadow-glow transition-all duration-300;
  }

  .faq-accordion-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px -8px rgba(14, 165, 233, 0.4);
  }

  .faq-question-text {
    @apply text-xl font-bold text-neutral-900 transition-colors duration-300 group-hover:text-brand-600;
  }

  .faq-answer-content {
    @apply px-8 pb-6 pt-2 text-lg text-neutral-700 leading-relaxed;
  }

  .faq-answer-highlight {
    @apply font-bold text-brand-700 bg-brand-50 px-2 py-1 rounded;
  }

  .faq-metrics-card {
    @apply bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-aviation border transition-all duration-300 hover:shadow-aviation-hover hover:-translate-y-1;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(14, 165, 233, 0.03) 100%);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  .faq-metrics-card:hover {
    box-shadow: 0 12px 40px -8px rgba(14, 165, 233, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                0 0 20px rgba(14, 165, 233, 0.1);
  }

  .faq-support-section {
    @apply bg-gradient-to-br from-brand-50 to-accent-50 rounded-3xl p-8 shadow-luxury border border-brand-100/50 text-center relative overflow-hidden;
  }

  .faq-cta-primary {
    @apply premium-button inline-flex items-center mr-4 mb-4;
    /* Fixed: removed group utility from @apply */
  }

  .faq-cta-secondary {
    @apply bg-white hover:bg-brand-50 text-brand-600 font-semibold px-6 py-3 rounded-xl border-2 border-brand-200 hover:border-brand-300 transition-all duration-300 shadow-soft hover:shadow-aviation focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transform hover:scale-105 inline-flex items-center mb-4;
  }

  /* FAQ Search Animations */
  @keyframes search-pulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(14, 165, 233, 0);
    }
  }

  .faq-search-active {
    animation: search-pulse 2s infinite;
  }

  @keyframes faq-highlight {
    0% {
      background-color: transparent;
    }
    50% {
      background-color: rgba(251, 191, 36, 0.2);
    }
    100% {
      background-color: transparent;
    }
  }

  .faq-search-highlight {
    animation: faq-highlight 1.5s ease-in-out;
  }

  /* FAQ Responsive Design Enhancements */
  @media (max-width: 768px) {
    .faq-search-input {
      @apply px-4 py-3 pl-12 pr-10 text-base;
    }

    .faq-search-icon {
      left: 12px;
    }

    .faq-clear-button {
      right: 12px;
    }

    .faq-accordion-button {
      @apply px-6 py-4;
    }

    .faq-answer-content {
      @apply px-6 pb-4 pt-2 text-base;
    }

    .faq-question-text {
      @apply text-lg;
    }

    .faq-accordion-icon {
      @apply w-8 h-8;
    }

    .faq-metrics-card {
      @apply p-4;
    }

    .faq-support-section {
      @apply p-6;
    }

    .faq-cta-primary,
    .faq-cta-secondary {
      @apply w-full justify-center mb-3;
    }
  }

  /* FAQ High Specificity Overrides */
  .faq-page .faq-accordion-item {
    position: relative;
    z-index: 1;
  }

  .faq-page .faq-accordion-item:hover {
    z-index: 2;
  }

  .faq-page .faq-search-input:focus {
    z-index: 10;
  }

  /* Prevent conflicts with existing styles */
  .faq-page .premium-card {
    /* Reset any conflicting premium-card styles for FAQ items */
  }

  /* £20,000+ Premium Component Styles */

  /* Luxury Card System */
  .card-luxury {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-luxury:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 32px 64px -12px rgba(14, 165, 233, 0.3),
                0 16px 32px -8px rgba(14, 165, 233, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .card-luxury-aviation {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(14, 165, 233, 0.2);
  }

  .card-luxury-accent {
    background: linear-gradient(135deg,
      rgba(234, 179, 8, 0.1) 0%,
      rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(234, 179, 8, 0.2);
  }

  /* Premium Button System */
  .btn-luxury-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    font-weight: 700;
    padding: 16px 32px;
    border-radius: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .btn-luxury-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
    transition: left 0.6s ease;
  }

  .btn-luxury-primary:hover::before {
    left: 100%;
  }

  .btn-luxury-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 16px 48px -8px rgba(14, 165, 233, 0.5),
                0 8px 24px -4px rgba(14, 165, 233, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .btn-luxury-primary:active {
    transform: translateY(0) scale(1.02);
  }

  .btn-luxury-secondary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    color: #0ea5e9;
    font-weight: 600;
    padding: 16px 32px;
    border-radius: 16px;
    border: 1px solid rgba(14, 165, 233, 0.2);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.2);
  }

  .btn-luxury-secondary:hover {
    background: rgba(14, 165, 233, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.3);
  }

  /* Premium Input System */
  .input-luxury {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 16px;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1);
  }

  .input-luxury:focus {
    outline: none;
    border-color: #0ea5e9;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 32px -8px rgba(14, 165, 233, 0.3),
                0 0 0 3px rgba(14, 165, 233, 0.1);
    transform: translateY(-2px);
  }

  .input-luxury::placeholder {
    color: #94a3b8;
    font-weight: 400;
  }

  /* Premium Navigation */
  .nav-luxury {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(14, 165, 233, 0.1);
    box-shadow: 0 4px 16px -4px rgba(14, 165, 233, 0.1);
  }

  .nav-luxury-item {
    color: #475569;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .nav-luxury-item:hover {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
    transform: translateY(-1px);
  }

  .nav-luxury-item.active {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.15);
  }

  .nav-luxury-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, #0ea5e9, #eab308);
    border-radius: 1px;
  }
}
