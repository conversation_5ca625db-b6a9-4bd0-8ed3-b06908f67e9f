import React, { useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';

import FloatingCTA from '../components/FloatingCTA';

const HowItWorksPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  // Set page title and meta description
  useEffect(() => {
    document.title = 'How It Works | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Learn how to create your verifiable onward flight reservation in 3 easy steps. Perfect for visa applications and travel proof. Instant download.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservations';
    };
  }, []);

  const steps = [
    {
      number: "1",
      title: "Fill out your travel details",
      icon: "Search",
      description: "Select your departure and destination airports with your preferred travel date."
    },
    {
      number: "2",
      title: "Confirm and make payment",
      icon: "User",
      description: "Review your flight details and complete secure payment."
    },
    {
      number: "3",
      title: "Receive your visa-ready flight reservation instantly",
      icon: "Download",
      description: "Download your professional flight reservation document immediately."
    }
  ];

  const benefits = [
    "Real flight routes from global airlines",
    "Visa-safe format trusted by embassies",
    "Instant delivery — no waiting",
    "One-time fee of $4.99 — no hidden costs",
    "Supports up to 2 travelers per reservation"
  ];

  const SearchIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  );

  const UserIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  );

  const DownloadIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );

  const getIcon = (iconName) => {
    switch (iconName) {
      case 'Search': return <SearchIcon />;
      case 'User': return <UserIcon />;
      case 'Download': return <DownloadIcon />;
      default: return <SearchIcon />;
    }
  };

  return (
    <div className="min-h-screen">
      {/* Premium Aviation Header Section */}
      <section className="relative min-h-screen flex items-center aviation-gradient-hero overflow-hidden">
        {/* Premium Aviation Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/8 via-transparent to-accent-500/8"></div>
        <div className="absolute top-20 left-10 w-80 h-80 bg-brand-400/15 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/15 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/10 to-accent-300/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <motion.h1
              className="aviation-hero-text mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
            >
              How It{' '}
              <span className="bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent">
                Works
              </span>
            </motion.h1>

            <motion.div
              className="max-w-4xl mx-auto mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4, ease: "easeOut" }}
            >
              <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-6">
                Create your professional, embassy-approved flight reservation in 3 simple steps. Get visa-ready documents delivered in just 60 seconds.
              </p>
            </motion.div>
          </motion.div>

          {/* Premium Process Badge */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-white/90 backdrop-blur-md text-brand-700 px-8 py-4 rounded-2xl text-base font-bold mb-8 shadow-aviation border border-brand-200">
              <div className="w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center mr-3">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              Simple Process, Professional Results
            </div>

            <h2 className="text-4xl md:text-5xl font-black text-brand-800 mb-6 leading-tight">
              It's Quick and
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block">
                Easy to Get Started
              </span>
            </h2>

            <div className="max-w-4xl mx-auto">
              <p className="text-lg md:text-xl text-brand-700 leading-relaxed font-medium">
                Simple process to get your flight reservation.
              </p>
            </div>
          </motion.div>

          {/* Premium Aviation Steps Timeline */}
          <div className="relative">
            {/* Timeline connector line */}
            <div className="hidden lg:block absolute top-24 left-1/2 transform -translate-x-1/2 w-full max-w-4xl">
              <div className="flex justify-between items-center px-32">
                <motion.div
                  initial={{ scaleX: 0 }}
                  whileInView={{ scaleX: 1 }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="h-1 bg-gradient-to-r from-brand-400 to-accent-400 rounded-full flex-1 mx-4"
                ></motion.div>
                <motion.div
                  initial={{ scaleX: 0 }}
                  whileInView={{ scaleX: 1 }}
                  transition={{ duration: 1, delay: 0.8 }}
                  className="h-1 bg-gradient-to-r from-brand-400 to-accent-400 rounded-full flex-1 mx-4"
                ></motion.div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 relative">
              {/* Desktop connecting lines */}
              <div className="hidden lg:block absolute top-1/2 left-1/3 w-1/3 h-0.5 bg-gradient-to-r from-brand-300/30 to-accent-300/30 transform -translate-y-1/2 z-0">
                <motion.div
                  className="h-full bg-gradient-to-r from-brand-500 to-accent-500"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 1.5, delay: 1, ease: "easeOut" }}
                />
              </div>
              <div className="hidden lg:block absolute top-1/2 right-1/3 w-1/3 h-0.5 bg-gradient-to-r from-brand-300/30 to-accent-300/30 transform -translate-y-1/2 z-0">
                <motion.div
                  className="h-full bg-gradient-to-r from-brand-500 to-accent-500"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 1.5, delay: 1.5, ease: "easeOut" }}
                />
              </div>

              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50, x: -30 }}
                  whileInView={{ opacity: 1, y: 0, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.3, ease: "easeOut" }}
                  className="card-aviation text-center group relative step-card-enhanced z-10"
                >
                  {/* Premium aviation shine effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-3xl"></div>

                  <div className="relative z-10">
                    {/* Premium Step Number with Aviation Design */}
                    <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-brand-500 via-brand-600 to-brand-700 text-white rounded-3xl text-3xl font-black mb-6 mx-auto shadow-aviation group-hover:shadow-aviation-hover transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                      {step.number}
                    </div>

                    {/* Premium Icon and Title */}
                    <div className="mb-6">
                      <div className="flex items-center justify-center text-brand-600 mb-4 group-hover:text-accent-600 transition-all duration-300">
                        <motion.div
                          className="w-20 h-20 bg-gradient-to-br from-brand-100 to-brand-50 rounded-3xl flex items-center justify-center group-hover:bg-gradient-to-br group-hover:from-accent-100 group-hover:to-accent-50 transition-all duration-300 shadow-soft group-hover:shadow-aviation"
                          whileHover={{
                            scale: 1.15,
                            rotate: 5,
                            boxShadow: "0 12px 32px -8px rgba(14, 165, 233, 0.3)"
                          }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          <div className="w-9 h-9 group-hover:scale-110 transition-transform duration-300">
                            {getIcon(step.icon)}
                          </div>
                        </motion.div>
                      </div>
                      <h3 className="text-2xl font-black text-brand-700 group-hover:text-brand-800 transition-colors mb-3">
                        {step.title}
                      </h3>
                    </div>

                    {/* Enhanced Description */}
                    <p className="text-neutral-700 leading-relaxed text-base font-medium">
                      {step.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>



      {/* Premium Benefits Section */}
      <section className="section-padding why-use-section-bg relative overflow-hidden">
        {/* Premium background elements */}
        <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-accent-100 text-accent-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
              Why 75,000+ Travelers Choose Us
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Why Use
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block md:inline md:ml-2">
                VerifiedOnward?
              </span>
            </h2>

            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              We've perfected every detail to ensure your visa application succeeds.
              <strong className="text-neutral-800">Here's what makes us the #1 choice for travelers worldwide.</strong>
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="premium-card relative overflow-hidden p-10"
            >
              {/* Premium background effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>

              <div className="relative z-10">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                  {benefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.15, ease: "easeOut" }}
                      className="flex items-start gap-4 group cursor-pointer"
                      whileHover={{ x: 5 }}
                    >
                      <motion.div
                        className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-accent-400 to-accent-500 rounded-full flex items-center justify-center shadow-success-glow checkmark-enhanced"
                        whileHover={{
                          scale: 1.2,
                          rotate: 360,
                          boxShadow: "0 8px 25px -8px rgba(34, 197, 94, 0.6)"
                        }}
                        transition={{ type: "spring", stiffness: 300, damping: 15 }}
                      >
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </motion.div>
                      <span className="text-lg text-neutral-700 leading-relaxed group-hover:text-neutral-800 transition-colors font-medium">
                        {benefit}
                      </span>
                    </motion.div>
                  ))}
                </div>

                {/* Removed secondary CTA to keep only one at bottom */}
              </div>

              {/* Premium shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Statistics and CTA Section */}
      <section className="section-padding bg-gradient-to-br from-brand-50 to-accent-50 relative overflow-hidden">
        <div className="container-modern relative z-10">
          {/* Premium Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-8 mb-16">
            {[
              { text: "60s Instant Delivery", delay: 0.2 },
              { text: "99.7% Embassy Acceptance", delay: 0.4 },
              { text: "$4.99 Fixed Price", delay: 0.6 }
            ].map((badge, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: badge.delay, ease: "easeOut" }}
                whileHover={{
                  y: -4,
                  scale: 1.05,
                  boxShadow: "0 12px 32px -8px rgba(34, 197, 94, 0.4)"
                }}
                className="flex items-center bg-white/90 backdrop-blur-md px-6 py-4 rounded-2xl shadow-aviation border border-green-200 trust-badge-enhanced cursor-pointer"
              >
                <motion.div
                  className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3"
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
                <span className="text-green-700 font-bold text-lg">{badge.text}</span>
              </motion.div>
            ))}
          </div>

          {/* Premium CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-center"
          >
            <motion.div
              animate={{
                boxShadow: [
                  "0 8px 32px -8px rgba(14, 165, 233, 0.3)",
                  "0 12px 40px -8px rgba(14, 165, 233, 0.5)",
                  "0 8px 32px -8px rgba(14, 165, 233, 0.3)"
                ]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="inline-block rounded-2xl"
            >
              <Link
                className="inline-flex items-center premium-button text-xl px-12 py-6 relative overflow-hidden group"
                {...getCTANavigationProps(navigate, location.pathname)}
              >
                <motion.div
                  whileHover={{
                    scale: 1.05,
                    background: "linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)"
                  }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center"
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  <motion.svg
                    className="w-6 h-6 mr-3 relative z-10"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    whileHover={{ rotate: 15 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </motion.svg>
                  <span className="relative z-10 font-black">Start Your $4.99 Reservation</span>
                  <motion.svg
                    className="w-6 h-6 ml-3 relative z-10"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    whileHover={{ x: 4 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </motion.svg>
                </motion.div>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Footer Transition Gradient */}
      <div className="footer-transition-gradient"></div>




      {/* Premium Floating CTA */}
      <FloatingCTA />
    </div>
  );
};

export default HowItWorksPage;
