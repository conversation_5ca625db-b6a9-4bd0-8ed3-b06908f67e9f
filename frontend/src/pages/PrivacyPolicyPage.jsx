import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';

import FloatingCTA from '../components/FloatingCTA';

const PrivacyPolicyPage = () => {
  const [openFAQ, setOpenFAQ] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Set page title and enhanced meta description for SEO
  useEffect(() => {
    document.title = 'Privacy Policy | VerifiedOnward - Secure Data Protection & GDPR Compliance';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'VerifiedOnward Privacy Policy: Learn how we protect your personal data with 256-bit SSL encryption, GDPR compliance, and transparent privacy practices for our embassy-approved flight reservation service.';

    // Add structured data for FAQ schema
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What personal information does VerifiedOnward collect?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We collect only essential information needed to create your flight reservation: passenger names, email address, and travel details. Payment information is processed securely through Stripe/PayPal and never stored on our servers."
          }
        },
        {
          "@type": "Question",
          "name": "How does VerifiedOnward protect my data?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We use 256-bit SSL encryption, industry-standard security protocols, and never share your data with third parties. All data transmission is encrypted and we maintain zero data breaches record."
          }
        },
        {
          "@type": "Question",
          "name": "Can I request deletion of my personal data?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, under GDPR compliance, you can request data deletion at any time by contacting our privacy <NAME_EMAIL>. We provide full transparency about data usage and respect your privacy rights."
          }
        }
      ]
    };

    // Remove existing schema scripts
    document.querySelectorAll('script[type="application/ld+json"][data-schema="privacy"]').forEach(script => script.remove());

    // Add structured data script
    const schemaScript = document.createElement('script');
    schemaScript.type = 'application/ld+json';
    schemaScript.setAttribute('data-schema', 'privacy');
    schemaScript.textContent = JSON.stringify(structuredData);
    document.head.appendChild(schemaScript);

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
      // Remove structured data scripts
      document.querySelectorAll('script[type="application/ld+json"][data-schema="privacy"]').forEach(script => script.remove());
    };
  }, []);

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="min-h-screen">


      {/* Premium Floating CTA */}
      <FloatingCTA />

      {/* Enhanced Privacy Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-brand-50 via-white to-accent-50 overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/3 to-accent-500/5"></div>
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-brand-400/10 to-brand-600/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-br from-accent-400/10 to-accent-600/5 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Trust Badge */}
            <div className="inline-flex items-center bg-green-100 text-green-800 px-6 py-3 rounded-full text-sm font-bold mb-8">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              🔒 Your Privacy is Our Priority • GDPR Compliant
            </div>

            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              Privacy{' '}
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">
                Policy
              </span>
            </h1>


          </motion.div>
        </div>
      </section>

      {/* Enhanced Content Section */}
      <section className="py-20 bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/3 to-accent-500/3"></div>
        <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/8 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/8 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-bold mb-8">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 8A6 6 0 006 8v2.5a2 2 0 01-2 2v2a2 2 0 002 2h12a2 2 0 002-2v-2a2 2 0 01-2-2V8z" clipRule="evenodd" />
              </svg>
              🛡️ Transparent Privacy Practices
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-4">
              How We Protect Your Data
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full mb-6"></div>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Clear, comprehensive information about our data collection, usage, and protection practices.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="premium-card max-w-6xl mx-auto p-12 relative overflow-hidden"
          >
            {/* Premium background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-brand-500/3 to-accent-500/3"></div>

            <div className="relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {[
                  {
                    number: "1",
                    title: "What Information We Collect",
                    icon: "📋",
                    content: [
                      "**Personal Details:** Only essential information needed for your flight reservation - passenger names, email address, and travel details",
                      "**Payment Information:** Processed securely through Stripe/PayPal with industry-standard encryption - never stored on our servers",
                      "**Technical Data:** Basic website usage analytics to improve our service (anonymized and aggregated only)"
                    ]
                  },
                  {
                    number: "2",
                    title: "How We Use Your Information",
                    icon: "✈️",
                    content: [
                      "**Create Your Reservation:** Generate authentic flight reservations with real airline data for your visa applications",
                      "**Deliver Your Documents:** Send your reservation instantly via email and provide download access",
                      "**Customer Support:** Respond to your questions and provide assistance when needed",
                      "**Service Improvement:** Analyze usage patterns to enhance our platform (data is anonymized)"
                    ]
                  },
                  {
                    number: "3",
                    title: "How We Protect Your Data",
                    icon: "🛡️",
                    content: [
                      "**Bank-Level Encryption:** 256-bit SSL encryption protects all data transmission and storage",
                      "**Secure Infrastructure:** Industry-leading security protocols and regular security audits",
                      "**No Data Sharing:** We never sell, rent, or share your personal information with third parties",
                      "**Access Controls:** Strict internal access controls ensure only authorized personnel can access data"
                    ]
                  },
                  {
                    number: "4",
                    title: "Your Privacy Rights",
                    icon: "⚖️",
                    content: [
                      "**Data Access:** Request a copy of all personal data we hold about you",
                      "**Data Deletion:** Request complete deletion of your personal information at any time",
                      "**Data Correction:** Update or correct any inaccurate personal information",
                      "**Withdraw Consent:** Opt out of non-essential communications and data processing"
                    ]
                  }
                ].map((section, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-3xl p-8 shadow-aviation border-2 border-brand-100 hover:shadow-aviation-hover transition-all duration-300 group relative overflow-hidden"
                  >
                    {/* Premium shine effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                    <div className="relative z-10">
                      <div className="flex items-start mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-brand-500 to-accent-500 rounded-2xl flex items-center justify-center text-white font-bold mr-4 shadow-glow group-hover:scale-110 transition-transform">
                          {section.number}
                        </div>
                        <div className="flex-1">
                          <div className="text-3xl mb-2">{section.icon}</div>
                          <h3 className="text-2xl font-bold text-neutral-900 group-hover:text-brand-600 transition-colors">
                            {section.title}
                          </h3>
                        </div>
                      </div>
                      <div className="space-y-4">
                        {section.content.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-start text-neutral-700">
                            <div className="w-2 h-2 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full mt-2 mr-4 flex-shrink-0"></div>
                            <span
                              className="leading-relaxed text-base"
                              dangerouslySetInnerHTML={{
                                __html: item.replace(/\*\*(.*?)\*\*/g, '<strong class="text-brand-700 font-semibold">$1</strong>')
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Additional Important Sections */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
              >
                <div className="bg-gradient-to-br from-blue-50 to-white rounded-3xl p-8 border-2 border-blue-100 shadow-soft">
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 flex items-center">
                    <span className="text-3xl mr-3">🍪</span>
                    Cookies & Analytics
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    We use essential cookies for site functionality and anonymized analytics to improve our service. No personal data is collected through cookies, and you can disable them in your browser settings.
                  </p>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-white rounded-3xl p-8 border-2 border-green-100 shadow-soft">
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 flex items-center">
                    <span className="text-3xl mr-3">🔄</span>
                    Policy Updates
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    We may update this policy to reflect service improvements or legal requirements. We'll notify users of significant changes via email and update the "Last Modified" date above.
                  </p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-white rounded-3xl p-8 border-2 border-purple-100 shadow-soft">
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 flex items-center">
                    <span className="text-3xl mr-3">🌍</span>
                    International Users
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    We serve users globally and comply with international privacy laws including GDPR, CCPA, and other regional regulations to ensure your privacy rights are protected worldwide.
                  </p>
                </div>
              </motion.div>

              {/* FAQ Section for AI & SEO Optimization */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="mt-16"
              >
                <div className="text-center mb-12">
                  <h3 className="text-3xl font-bold text-brand-800 mb-4">Privacy FAQ</h3>
                  <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full mb-4"></div>
                  <p className="text-neutral-600 max-w-2xl mx-auto">
                    Common questions about how we protect and handle your personal information.
                  </p>
                </div>

                <div className="space-y-4 max-w-4xl mx-auto">
                  {[
                    {
                      question: "What personal information does VerifiedOnward collect?",
                      answer: "We collect only essential information needed to create your flight reservation: passenger names, email address, and travel details. Payment information is processed securely through Stripe/PayPal and never stored on our servers."
                    },
                    {
                      question: "How does VerifiedOnward protect my data?",
                      answer: "We use 256-bit SSL encryption, industry-standard security protocols, and never share your data with third parties. All data transmission is encrypted and we maintain a perfect security record with zero data breaches."
                    },
                    {
                      question: "Can I request deletion of my personal data?",
                      answer: "Yes, under GDPR compliance, you can request data deletion at any time by contacting our privacy <NAME_EMAIL>. We provide full transparency about data usage and respect your privacy rights."
                    },
                    {
                      question: "Does VerifiedOnward share my information with third parties?",
                      answer: "No, we never sell, rent, or share your personal information with third parties. Your data is used solely to provide our flight reservation service and improve your experience with us."
                    },
                    {
                      question: "How long do you keep my personal data?",
                      answer: "We retain your data only as long as necessary to provide our service and comply with legal obligations. You can request data deletion at any time, and we'll remove your information from our systems promptly."
                    }
                  ].map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden"
                    >
                      <button
                        onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                        className="w-full px-6 py-5 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-inset"
                        aria-expanded={openFAQ === index}
                        aria-controls={`privacy-faq-answer-${index}`}
                      >
                        <h4 className="text-lg font-semibold text-gray-900 pr-4">
                          {faq.question}
                        </h4>
                        <svg
                          className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                            openFAQ === index ? 'transform rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>

                      <AnimatePresence>
                        {openFAQ === index && (
                          <motion.div
                            id={`privacy-faq-answer-${index}`}
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-5 pt-2">
                              <p className="text-gray-700 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Enhanced Contact Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.0 }}
                className="mt-16 text-center bg-gradient-to-br from-brand-50 to-accent-50 rounded-3xl p-12 border-2 border-brand-100 shadow-aviation"
              >
                <div className="text-4xl mb-4">🔒</div>
                <h3 className="text-2xl font-bold text-neutral-900 mb-4">
                  Questions About Your Privacy?
                </h3>
                <p className="text-neutral-700 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
                  We're committed to complete transparency about how we handle your data.
                  If you have questions about your privacy rights or want to exercise them, our privacy team is here to help.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center bg-brand-600 hover:bg-brand-700 text-white px-8 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 group"
                  >
                    <svg className="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact Privacy Team
                  </a>
                  <div className="text-sm text-neutral-600">
                    ✓ Typically respond within 24 hours • ✓ GDPR compliant • ✓ Full transparency
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Premium shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
          </motion.div>
        </div>
      </section>


    </div>
  );
};

export default PrivacyPolicyPage;
