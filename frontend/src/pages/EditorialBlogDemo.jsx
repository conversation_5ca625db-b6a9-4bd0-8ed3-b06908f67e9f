import React, { useEffect } from 'react';
import EditorialBlogTemplate from '../components/EditorialBlogTemplate';

const EditorialBlogDemo = () => {
  useEffect(() => {
    // Set page title
    document.title = 'Editorial Blog Template Demo - VerifiedOnward';
    
    // Scroll to top on mount
    window.scrollTo(0, 0);
  }, []);

  // Demo blog post data
  const demoPost = {
    id: 'demo',
    slug: 'editorial-blog-demo',
    title: 'How to Get a Dummy Ticket for Any Visa in 2025',
    excerpt: 'Learn the safest way to provide flight proof for visa applications',
    tags: ['Visa Tips', 'Travel Guide', 'Embassy Requirements'],
    publishDate: '2025-01-20',
    readTime: '8 min read',
    thumbnail: {
      type: 'gradient',
      colors: ['from-blue-500', 'to-indigo-600'],
      icon: '✈️',
      pattern: 'flight'
    },
    metaTitle: 'How to Get a Dummy Ticket for Any Visa 2025 - Complete Editorial Guide',
    metaDescription: 'Complete editorial guide to getting dummy tickets for any visa application in 2025. Expert storytelling with subtle conversion elements.',
    content: `
# How to Get a Dummy Ticket for Any Visa in 2025

This is a demo of the editorial blog template that combines expert storytelling with subtle conversion elements.

## What You'll Learn

- The safest way to provide flight proof for visa applications
- How to avoid costly mistakes that lead to rejections
- Country-specific requirements and best practices
- Why professional services are recommended by travel experts

## Getting Started

The editorial template focuses on building authority first, then naturally introducing product mentions in a helpful, non-aggressive way.

**Key Features:**
- Clean hero section with gradient highlights
- Authority-building introduction
- Step-by-step educational content
- Natural product recommendations
- Embedded testimonials
- Trust-based final conversion

This template is designed to feel like reading a premium travel magazine article while subtly guiding readers toward conversion.
    `
  };

  return <EditorialBlogTemplate post={demoPost} />;
};

export default EditorialBlogDemo;
