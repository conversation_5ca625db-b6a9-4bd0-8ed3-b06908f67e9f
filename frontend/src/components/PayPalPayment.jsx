import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { paymentAPI } from '../services/api';

const PayPalPayment = ({ amount, onSuccess, onError, disabled, isProcessing: externalIsProcessing, setIsProcessing: externalSetIsProcessing }) => {
  const [internalIsProcessing, setInternalIsProcessing] = useState(false);

  // Use external processing state if provided, otherwise use internal
  const isProcessing = externalIsProcessing !== undefined ? externalIsProcessing : internalIsProcessing;
  const setIsProcessing = externalSetIsProcessing || setInternalIsProcessing;
  const [paypalLoaded, setPaypalLoaded] = useState(false);

  useEffect(() => {
    // Load PayPal SDK
    if (!window.paypal) {
      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${import.meta.env.VITE_PAYPAL_CLIENT_ID || 'demo'}&currency=USD`;
      script.onload = () => setPaypalLoaded(true);
      document.body.appendChild(script);
    } else {
      setPaypalLoaded(true);
    }
  }, []);

  useEffect(() => {
    if (paypalLoaded && window.paypal && !disabled) {
      // Clear any existing PayPal buttons
      const container = document.getElementById('paypal-button-container');
      if (container) {
        container.innerHTML = '';
      }

      // Render PayPal button
      window.paypal.Buttons({
        createOrder: async () => {
          if (disabled || isProcessing) {
            throw new Error('Payment is disabled or already processing');
          }
          try {
            setIsProcessing(true);
            const response = await paymentAPI.createPayPalOrder(amount);
            return response.orderId;
          } catch (error) {
            onError(error.message);
            setIsProcessing(false);
            throw error;
          }
        },
        onApprove: async (data) => {
          try {
            const response = await paymentAPI.capturePayPalPayment(data.orderID);
            if (response.success) {
              onSuccess({
                success: true,
                paymentId: response.paymentId
              });
            } else {
              onError('Payment was not completed');
            }
          } catch (error) {
            onError(error.message);
          } finally {
            setIsProcessing(false);
          }
        },
        onError: (err) => {
          console.error('PayPal error:', err);
          onError('PayPal payment failed');
          setIsProcessing(false);
        },
        onCancel: () => {
          setIsProcessing(false);
        },
        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'paypal'
        }
      }).render('#paypal-button-container');
    }
  }, [paypalLoaded, amount, onSuccess, onError, disabled, isProcessing]);

  // Check if we're in demo mode
  const isDemoMode = !process.env.REACT_APP_PAYPAL_CLIENT_ID ||
                     process.env.REACT_APP_PAYPAL_CLIENT_ID === 'your_paypal_client_id';

  // Demo PayPal payment for development
  const handleDemoPayment = async () => {
    if (disabled || isProcessing) {
      return;
    }

    setIsProcessing(true);

    // Simulate PayPal payment processing
    setTimeout(() => {
      onSuccess({
        success: true,
        paymentId: 'paypal_demo_' + Date.now()
      });
      setIsProcessing(false);
    }, 2000);
  };

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        🅿️ Pay with PayPal
      </h3>

      {isDemoMode ? (
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">🎭 Demo Mode</h4>
            <p className="text-sm text-blue-600 mb-3">
              This is a demo PayPal payment. Click the button below to simulate a successful payment.
            </p>
            <div className="text-xs text-blue-500">
              <p>• No real PayPal payment will be processed</p>
              <p>• No PayPal account is required</p>
              <p>• Payment will be simulated automatically</p>
            </div>
          </div>

          <motion.button
            onClick={handleDemoPayment}
            disabled={disabled || isProcessing}
            className="w-full bg-yellow-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-yellow-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Simulating PayPal Payment...
              </div>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <span>Pay</span>
                <span className="price-glow text-white font-black">
                  ${amount.toFixed(2)}
                </span>
                <span>with PayPal (Demo)</span>
              </span>
            )}
          </motion.button>
        </div>
      ) : (
        <div>
          {/* PayPal Button Container */}
          <div id="paypal-button-container" className="mb-4"></div>

          {/* Fallback Demo Button for Development */}
          <motion.button
            onClick={handleDemoPayment}
            disabled={isProcessing}
            className="w-full bg-yellow-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-yellow-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Processing PayPal Payment...
              </div>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <span>Fallback Demo PayPal -</span>
                <span className="price-glow text-white font-black">
                  ${amount.toFixed(2)}
                </span>
              </span>
            )}
          </motion.button>
        </div>
      )}

      <div className="text-xs text-gray-500 text-center mt-4">
        <p>🔒 Secure PayPal payment processing</p>
        {isDemoMode ? (
          <p>🎭 Demo mode - no real payment required</p>
        ) : (
          <p>💡 Demo mode - click the demo button to simulate payment</p>
        )}
      </div>
    </div>
  );
};

export default PayPalPayment;
