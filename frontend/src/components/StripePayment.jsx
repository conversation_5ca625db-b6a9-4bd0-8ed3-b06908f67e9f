import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { motion } from 'framer-motion';
import { paymentAPI } from '../services/api';

// Check if we're in demo mode
const isDemoMode = !import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY ||
                   import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY === 'pk_test_your_stripe_publishable_key';

// Initialize Stripe only if not in demo mode
const stripePromise = isDemoMode ? null : loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

const CheckoutForm = ({ amount, onSuccess, onError, disabled, isProcessing, setIsProcessing }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = useState(null);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (disabled || isProcessing) {
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Create payment intent on the server
      const { clientSecret, paymentIntentId } = await paymentAPI.createStripeIntent(amount, {
        description: 'Dummy Flight Ticket'
      });

      // Demo mode - simulate successful payment
      if (isDemoMode || clientSecret.includes('demo')) {
        console.log('🎭 Demo mode: Simulating Stripe payment success');

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        onSuccess({
          success: true,
          paymentId: paymentIntentId
        });
        return;
      }

      // Real Stripe payment flow
      if (!stripe || !elements) {
        throw new Error('Stripe not initialized');
      }

      // Confirm payment with Stripe
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement),
          billing_details: {
            name: 'Customer Name',
          },
        }
      });

      if (stripeError) {
        setError(stripeError.message);
        onError(stripeError.message);
      } else if (paymentIntent.status === 'succeeded') {
        onSuccess({
          success: true,
          paymentId: paymentIntent.id
        });
      }
    } catch (err) {
      setError(err.message);
      onError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '18px',
        color: '#1e293b',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontWeight: '500',
        '::placeholder': {
          color: '#94a3b8',
        },
        padding: '16px',
        lineHeight: '24px',
      },
      invalid: {
        color: '#dc2626',
        iconColor: '#dc2626',
      },
      complete: {
        color: '#059669',
        iconColor: '#059669',
      },
    },
    hidePostalCode: true,
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {isDemoMode ? (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-2">🎭 Demo Mode</h4>
          <p className="text-sm text-blue-600 mb-3">
            This is a demo payment form. Click "Pay" to simulate a successful payment.
          </p>
          <div className="text-xs text-blue-500">
            <p>• No real payment will be processed</p>
            <p>• No credit card information is required</p>
            <p>• Payment will be simulated automatically</p>
          </div>
        </div>
      ) : (
        <div className="p-5 border-2 border-brand-200 rounded-2xl bg-white shadow-aviation focus-within:border-brand-500 focus-within:shadow-aviation-hover transition-all duration-300">
          <CardElement options={cardElementOptions} />
        </div>
      )}

      {error && (
        <div className="text-red-600 text-sm">{error}</div>
      )}

      <motion.button
        type="submit"
        disabled={disabled || isProcessing || (!isDemoMode && !stripe)}
        className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white py-4 px-8 rounded-2xl font-black text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 active:scale-95 touch-manipulation min-h-[56px]"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            {isDemoMode ? 'Simulating Payment...' : 'Processing Payment...'}
          </div>
        ) : (
          <span className="flex items-center justify-center gap-2">
            <span>Pay</span>
            <span className="price-glow text-white font-black">
              ${amount.toFixed(2)}
            </span>
          </span>
        )}
      </motion.button>

      <div className="text-xs text-gray-500 text-center">
        <p>🔒 Your payment information is secure and encrypted</p>
        {!isDemoMode && <p>💳 Test card: 4242 4242 4242 4242 (any future date, any CVC)</p>}
        {isDemoMode && <p>🎭 Demo mode - no real payment required</p>}
      </div>
    </form>
  );
};

const StripePayment = ({ amount, onSuccess, onError, disabled, isProcessing: externalIsProcessing, setIsProcessing: externalSetIsProcessing }) => {
  const [internalIsProcessing, setInternalIsProcessing] = useState(false);

  // Use external processing state if provided, otherwise use internal
  const isProcessing = externalIsProcessing !== undefined ? externalIsProcessing : internalIsProcessing;
  const setIsProcessing = externalSetIsProcessing || setInternalIsProcessing;

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        💳 Pay with Credit Card
      </h3>

      {isDemoMode ? (
        <CheckoutForm
          amount={amount}
          onSuccess={onSuccess}
          onError={onError}
          disabled={disabled}
          isProcessing={isProcessing}
          setIsProcessing={setIsProcessing}
        />
      ) : (
        <Elements stripe={stripePromise}>
          <CheckoutForm
            amount={amount}
            onSuccess={onSuccess}
            onError={onError}
            disabled={disabled}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
          />
        </Elements>
      )}
    </div>
  );
};

export default StripePayment;
