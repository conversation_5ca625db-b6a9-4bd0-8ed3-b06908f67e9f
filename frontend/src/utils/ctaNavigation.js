/**
 * Utility functions for consistent CTA button navigation behavior
 * Handles navigation to the flight search form from any page
 */

/**
 * Navigates to the flight search form with smooth scrolling
 * @param {Function} navigate - React Router navigate function
 * @param {string} currentPath - Current page path
 * @param {Object} options - Additional options
 * @param {number} options.delay - Delay before scrolling (for page transitions)
 * @param {string} options.block - Scroll alignment ('start', 'center', 'end', 'nearest')
 */
export const navigateToSearchForm = (navigate, currentPath = '/', options = {}) => {
  const { delay = 100, block = 'center' } = options;
  
  const scrollToSearchForm = () => {
    const searchForm = document.querySelector('#search-form');
    if (searchForm) {
      searchForm.scrollIntoView({ 
        behavior: 'smooth', 
        block: block 
      });
    }
  };

  // If already on homepage, just scroll to search form
  if (currentPath === '/') {
    setTimeout(scrollToSearchForm, delay);
  } else {
    // Navigate to homepage first, then scroll to search form
    navigate('/', { replace: false });
    // Wait for page to load before scrolling
    setTimeout(scrollToSearchForm, delay + 200);
  }
};

/**
 * Creates a click handler for CTA buttons that navigates to search form
 * @param {Function} navigate - React Router navigate function
 * @param {string} currentPath - Current page path
 * @param {Function} onBeforeNavigate - Optional callback before navigation
 * @returns {Function} Click handler function
 */
export const createCTAClickHandler = (navigate, currentPath = '/', onBeforeNavigate = null) => {
  return (e) => {
    e.preventDefault();
    
    // Call optional callback before navigation
    if (onBeforeNavigate && typeof onBeforeNavigate === 'function') {
      onBeforeNavigate();
    }
    
    navigateToSearchForm(navigate, currentPath);
  };
};

/**
 * Hook-like function to get CTA navigation props for Link components
 * @param {Function} navigate - React Router navigate function
 * @param {string} currentPath - Current page path
 * @param {Function} onBeforeNavigate - Optional callback before navigation
 * @returns {Object} Props object with to and onClick handlers
 */
export const getCTANavigationProps = (navigate, currentPath = '/', onBeforeNavigate = null) => {
  return {
    to: currentPath === '/' ? '#search-form' : '/#search-form',
    onClick: createCTAClickHandler(navigate, currentPath, onBeforeNavigate)
  };
};

/**
 * Utility to check if we're currently on the homepage
 * @param {string} pathname - Current pathname from useLocation
 * @returns {boolean} True if on homepage
 */
export const isHomePage = (pathname) => {
  return pathname === '/';
};

/**
 * Enhanced navigation function with error handling and fallbacks
 * @param {Function} navigate - React Router navigate function
 * @param {string} currentPath - Current page path
 * @param {Object} options - Additional options
 */
export const navigateToSearchFormSafe = (navigate, currentPath = '/', options = {}) => {
  try {
    navigateToSearchForm(navigate, currentPath, options);
  } catch (error) {
    console.warn('CTA Navigation Error:', error);
    // Fallback: just navigate to homepage
    if (currentPath !== '/') {
      navigate('/');
    }
  }
};
